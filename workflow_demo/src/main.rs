//! 工作流协调器演示程序
//! 
//! 这是一个独立的演示程序，展示了 RustScan 工作流协调器的核心功能

use actix_cors::Cors;
use actix_web::{middleware::Logger, web, App, HttpResponse, HttpServer, Result};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::collections::HashMap;
use std::process::Command;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tracing::{info, warn, error, Level};
use tracing_subscriber;
use uuid::Uuid;

/// 工作流步骤定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowStep {
    pub id: String,
    pub name: String,
    pub tool: String,
    pub parameters: HashMap<String, serde_json::Value>,
    pub depends_on: Vec<String>,
}

/// 工作流定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowDefinition {
    pub id: String,
    pub name: String,
    pub description: String,
    pub version: String,
    pub steps: Vec<WorkflowStep>,
    pub global_config: Option<HashMap<String, serde_json::Value>>,
}

/// 创建工作流实例请求
#[derive(Debug, Deserialize)]
pub struct CreateWorkflowInstanceRequest {
    pub workflow_id: String,
    pub name: String,
    pub target: String,
    pub context: Option<HashMap<String, serde_json::Value>>,
}

/// 工作流实例状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InstanceStatus {
    Created,
    Running,
    Completed,
    Failed,
    Cancelled,
}

/// 工作流步骤状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StepStatus {
    Pending,
    Running,
    Completed,
    Failed,
}

/// 工作流实例
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowInstance {
    pub instance_id: String,
    pub workflow_id: String,
    pub name: String,
    pub target: String,
    pub status: InstanceStatus,
    pub created_at: String,
    pub started_at: Option<String>,
    pub completed_at: Option<String>,
    pub current_step: Option<String>,
    pub progress: WorkflowProgress,
    pub steps: Vec<WorkflowStepExecution>,
}

/// 工作流进度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowProgress {
    pub completed_steps: usize,
    pub total_steps: usize,
    pub percentage: u8,
}

/// 工作流步骤执行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowStepExecution {
    pub id: String,
    pub name: String,
    pub tool: String,
    pub status: StepStatus,
    pub started_at: Option<String>,
    pub completed_at: Option<String>,
    pub execution_time: Option<u64>,
    pub output: Option<String>,
    pub error: Option<String>,
}

/// 工作流管理器
#[derive(Debug)]
pub struct WorkflowManager {
    pub instances: Arc<Mutex<HashMap<String, WorkflowInstance>>>,
}

impl WorkflowManager {
    pub fn new() -> Self {
        Self {
            instances: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub fn create_instance(&self, request: CreateWorkflowInstanceRequest) -> Result<WorkflowInstance, String> {
        let instance_id = Uuid::new_v4().to_string();
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let timestamp = chrono::DateTime::from_timestamp(now as i64, 0)
            .unwrap()
            .to_rfc3339();

        // 获取工作流模板
        let template = match request.workflow_id.as_str() {
            "basic_port_scan" => WorkflowDefinition::basic_port_scan(),
            "comprehensive_scan" => WorkflowDefinition::comprehensive_scan(),
            _ => return Err("无效的工作流ID".to_string()),
        };

        // 创建步骤执行状态
        let steps: Vec<WorkflowStepExecution> = template.steps.iter().map(|step| {
            WorkflowStepExecution {
                id: step.id.clone(),
                name: step.name.clone(),
                tool: step.tool.clone(),
                status: StepStatus::Pending,
                started_at: None,
                completed_at: None,
                execution_time: None,
                output: None,
                error: None,
            }
        }).collect();

        let instance = WorkflowInstance {
            instance_id: instance_id.clone(),
            workflow_id: request.workflow_id,
            name: request.name,
            target: request.target,
            status: InstanceStatus::Created,
            created_at: timestamp,
            started_at: None,
            completed_at: None,
            current_step: None,
            progress: WorkflowProgress {
                completed_steps: 0,
                total_steps: steps.len(),
                percentage: 0,
            },
            steps,
        };

        // 存储实例
        {
            let mut instances = self.instances.lock().unwrap();
            instances.insert(instance_id.clone(), instance.clone());
        }

        // 启动工作流执行
        self.start_workflow_execution(instance_id.clone());

        Ok(instance)
    }

    pub fn get_instance(&self, instance_id: &str) -> Option<WorkflowInstance> {
        let instances = self.instances.lock().unwrap();
        instances.get(instance_id).cloned()
    }

    pub fn get_all_instances(&self) -> Vec<WorkflowInstance> {
        let instances = self.instances.lock().unwrap();
        instances.values().cloned().collect()
    }

    pub fn start_workflow_execution(&self, instance_id: String) {
        let instances = self.instances.clone();

        thread::spawn(move || {
            Self::execute_workflow(instances, instance_id);
        });
    }

    fn execute_workflow(instances: Arc<Mutex<HashMap<String, WorkflowInstance>>>, instance_id: String) {
        info!("开始执行工作流: {}", instance_id);

        // 更新状态为运行中
        {
            let mut instances_guard = instances.lock().unwrap();
            if let Some(instance) = instances_guard.get_mut(&instance_id) {
                instance.status = InstanceStatus::Running;
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                instance.started_at = Some(chrono::DateTime::from_timestamp(now as i64, 0)
                    .unwrap()
                    .to_rfc3339());
            }
        }

        // 执行每个步骤
        let steps_count = {
            let instances_guard = instances.lock().unwrap();
            instances_guard.get(&instance_id).map(|i| i.steps.len()).unwrap_or(0)
        };

        for step_index in 0..steps_count {
            // 获取当前步骤信息
            let (step_id, step_name, step_tool, target) = {
                let instances_guard = instances.lock().unwrap();
                if let Some(instance) = instances_guard.get(&instance_id) {
                    let step = &instance.steps[step_index];
                    (step.id.clone(), step.name.clone(), step.tool.clone(), instance.target.clone())
                } else {
                    error!("工作流实例不存在: {}", instance_id);
                    return;
                }
            };

            info!("执行步骤: {} - {}", step_name, step_tool);

            // 更新步骤状态为运行中
            let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
            {
                let mut instances_guard = instances.lock().unwrap();
                if let Some(instance) = instances_guard.get_mut(&instance_id) {
                    instance.current_step = Some(step_id.clone());
                    instance.steps[step_index].status = StepStatus::Running;
                    instance.steps[step_index].started_at = Some(
                        chrono::DateTime::from_timestamp(start_time as i64, 0)
                            .unwrap()
                            .to_rfc3339()
                    );
                }
            }

            // 执行实际的工具命令
            let execution_result = Self::execute_tool(&step_tool, &target);
            let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
            let execution_time = (end_time - start_time) * 1000; // 转换为毫秒

            // 更新步骤执行结果
            {
                let mut instances_guard = instances.lock().unwrap();
                if let Some(instance) = instances_guard.get_mut(&instance_id) {
                    let step = &mut instance.steps[step_index];
                    step.completed_at = Some(
                        chrono::DateTime::from_timestamp(end_time as i64, 0)
                            .unwrap()
                            .to_rfc3339()
                    );
                    step.execution_time = Some(execution_time);

                    match execution_result {
                        Ok(output) => {
                            step.status = StepStatus::Completed;
                            step.output = Some(output);
                            instance.progress.completed_steps += 1;
                        }
                        Err(error) => {
                            step.status = StepStatus::Failed;
                            step.error = Some(error);
                            instance.status = InstanceStatus::Failed;
                            warn!("步骤执行失败: {} - {}", step_name, step.error.as_ref().unwrap());
                            return;
                        }
                    }

                    // 更新进度百分比
                    instance.progress.percentage =
                        ((instance.progress.completed_steps as f32 / instance.progress.total_steps as f32) * 100.0) as u8;
                }
            }

            info!("步骤完成: {} ({}ms)", step_name, execution_time);

            // 步骤间延迟
            thread::sleep(Duration::from_secs(2));
        }

        // 更新工作流状态为完成
        {
            let mut instances_guard = instances.lock().unwrap();
            if let Some(instance) = instances_guard.get_mut(&instance_id) {
                instance.status = InstanceStatus::Completed;
                instance.current_step = None;
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                instance.completed_at = Some(chrono::DateTime::from_timestamp(now as i64, 0)
                    .unwrap()
                    .to_rfc3339());
            }
        }

        info!("工作流执行完成: {}", instance_id);
    }

    fn execute_tool(tool: &str, target: &str) -> Result<String, String> {
        info!("执行工具: {} 目标: {}", tool, target);

        match tool {
            "target_validator" => Self::execute_target_validator(target),
            "rustscan" => Self::execute_rustscan(target),
            "nmap" => Self::execute_nmap(target),
            "dnsx" => Self::execute_dnsx(target),
            "subfinder" => Self::execute_subfinder(target),
            "httpx" => Self::execute_httpx(target),
            "crawl4ai" => Self::execute_crawl4ai(target),
            "nuclei" => Self::execute_nuclei(target),
            "result_analyzer" => Self::execute_result_analyzer(target),
            _ => Err(format!("不支持的工具: {}", tool)),
        }
    }

    fn execute_rustscan(target: &str) -> Result<String, String> {
        info!("执行 RustScan 扫描: {}", target);

        // 检查 rustscan 是否可用
        let output = Command::new("rustscan")
            .arg("--version")
            .output();

        if output.is_err() {
            warn!("RustScan 不可用，使用 nmap 替代");
            return Self::execute_nmap(target);
        }

        // 执行 rustscan
        let result = Command::new("rustscan")
            .arg("-a")
            .arg(target)
            .arg("--timeout")
            .arg("3000")
            .arg("--tries")
            .arg("1")
            .arg("--batch-size")
            .arg("5000")
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    Ok(format!("RustScan 扫描完成:\n{}", stdout))
                } else {
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    Err(format!("RustScan 执行失败: {}", stderr))
                }
            }
            Err(e) => Err(format!("RustScan 执行错误: {}", e)),
        }
    }

    fn execute_nmap(target: &str) -> Result<String, String> {
        info!("执行 Nmap 扫描: {}", target);

        let result = Command::new("nmap")
            .arg("-sS")
            .arg("-T4")
            .arg("--top-ports")
            .arg("1000")
            .arg(target)
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    Ok(format!("Nmap 扫描完成:\n{}", stdout))
                } else {
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    Err(format!("Nmap 执行失败: {}", stderr))
                }
            }
            Err(e) => Err(format!("Nmap 执行错误: {}", e)),
        }
    }

    fn execute_subfinder(target: &str) -> Result<String, String> {
        info!("执行 Subfinder 子域名发现: {}", target);

        let result = Command::new("subfinder")
            .arg("-d")
            .arg(target)
            .arg("-silent")
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    Ok(format!("Subfinder 扫描完成:\n{}", stdout))
                } else {
                    // Subfinder 不可用时使用简单的 DNS 查询
                    warn!("Subfinder 不可用，使用基础 DNS 查询");
                    Self::execute_basic_dns(target)
                }
            }
            Err(_) => {
                warn!("Subfinder 不可用，使用基础 DNS 查询");
                Self::execute_basic_dns(target)
            }
        }
    }

    fn execute_dnsx(target: &str) -> Result<String, String> {
        info!("执行 DNSx 解析: {}", target);

        let result = Command::new("dnsx")
            .arg("-d")
            .arg(target)
            .arg("-silent")
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    Ok(format!("DNSx 解析完成:\n{}", stdout))
                } else {
                    Self::execute_basic_dns(target)
                }
            }
            Err(_) => {
                warn!("DNSx 不可用，使用基础 DNS 查询");
                Self::execute_basic_dns(target)
            }
        }
    }

    fn execute_httpx(target: &str) -> Result<String, String> {
        info!("执行 HTTPx Web 探测: {}", target);

        let result = Command::new("httpx")
            .arg("-u")
            .arg(target)
            .arg("-silent")
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    Ok(format!("HTTPx 探测完成:\n{}", stdout))
                } else {
                    Self::execute_basic_http(target)
                }
            }
            Err(_) => {
                warn!("HTTPx 不可用，使用基础 HTTP 探测");
                Self::execute_basic_http(target)
            }
        }
    }

    fn execute_nuclei(target: &str) -> Result<String, String> {
        info!("执行 Nuclei 漏洞扫描: {}", target);

        let result = Command::new("nuclei")
            .arg("-u")
            .arg(target)
            .arg("-silent")
            .arg("-severity")
            .arg("medium,high,critical")
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    Ok(format!("Nuclei 扫描完成:\n{}", stdout))
                } else {
                    Ok("Nuclei 扫描完成，未发现漏洞".to_string())
                }
            }
            Err(_) => {
                warn!("Nuclei 不可用，跳过漏洞扫描");
                Ok("漏洞扫描跳过 (Nuclei 不可用)".to_string())
            }
        }
    }

    fn execute_basic_dns(target: &str) -> Result<String, String> {
        let result = Command::new("nslookup")
            .arg(target)
            .output();

        match result {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                Ok(format!("DNS 查询完成:\n{}", stdout))
            }
            Err(e) => Err(format!("DNS 查询失败: {}", e)),
        }
    }

    fn execute_basic_http(target: &str) -> Result<String, String> {
        let url = if target.starts_with("http") {
            target.to_string()
        } else {
            format!("http://{}", target)
        };

        let result = Command::new("curl")
            .arg("-I")
            .arg("-s")
            .arg("--connect-timeout")
            .arg("10")
            .arg(&url)
            .output();

        match result {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                Ok(format!("HTTP 探测完成:\n{}", stdout))
            }
            Err(e) => Err(format!("HTTP 探测失败: {}", e)),
        }
    }

    // 新增工具实现 - 按照文档流程顺序

    /// 目标验证
    fn execute_target_validator(target: &str) -> Result<String, String> {
        info!("执行目标验证: {}", target);

        // 1. DNS 验证
        let dns_result = Command::new("nslookup")
            .arg(target)
            .output();

        let dns_valid = match dns_result {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                !stdout.contains("NXDOMAIN") && !stdout.is_empty()
            }
            Err(_) => false,
        };

        // 2. 连通性验证
        let ping_result = Command::new("ping")
            .arg("-c")
            .arg("1")
            .arg("-W")
            .arg("3")
            .arg(target)
            .output();

        let connectivity_valid = match ping_result {
            Ok(output) => output.status.success(),
            Err(_) => false,
        };

        let validation_result = json!({
            "target": target,
            "dns_valid": dns_valid,
            "connectivity_valid": connectivity_valid,
            "overall_valid": dns_valid || connectivity_valid,
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        if dns_valid || connectivity_valid {
            Ok(format!("目标验证通过:\n{}", serde_json::to_string_pretty(&validation_result).unwrap()))
        } else {
            Err(format!("目标验证失败: {} 无法解析或连接", target))
        }
    }

    /// Crawl4AI 内容爬取
    fn execute_crawl4ai(target: &str) -> Result<String, String> {
        info!("执行 Crawl4AI 内容爬取: {}", target);

        // 检查 crawl4ai 是否可用
        let check_result = Command::new("python3")
            .arg("-c")
            .arg("import crawl4ai; print('available')")
            .output();

        if check_result.is_err() {
            warn!("Crawl4AI 不可用，使用基础爬取替代");
            return Self::execute_basic_crawl(target);
        }

        // 创建临时 Python 脚本执行爬取
        let python_script = format!(r#"
import asyncio
from crawl4ai import AsyncWebCrawler
import json

async def crawl_target():
    async with AsyncWebCrawler(verbose=True) as crawler:
        result = await crawler.arun(
            url="{}",
            word_count_threshold=10,
            extraction_strategy="LLMExtractionStrategy",
            chunking_strategy="RegexChunking",
            bypass_cache=True
        )

        return {{
            "url": result.url,
            "title": result.metadata.get("title", ""),
            "description": result.metadata.get("description", ""),
            "links_count": len(result.links.get("internal", [])) + len(result.links.get("external", [])),
            "internal_links": result.links.get("internal", [])[:10],
            "external_links": result.links.get("external", [])[:10],
            "content_length": len(result.markdown),
            "success": result.success
        }}

if __name__ == "__main__":
    result = asyncio.run(crawl_target())
    print(json.dumps(result, indent=2))
"#, target);

        let result = Command::new("python3")
            .arg("-c")
            .arg(&python_script)
            .output();

        match result {
            Ok(output) => {
                if output.status.success() {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    Ok(format!("Crawl4AI 爬取完成:\n{}", stdout))
                } else {
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    warn!("Crawl4AI 执行失败，使用基础爬取替代: {}", stderr);
                    Self::execute_basic_crawl(target)
                }
            }
            Err(e) => {
                warn!("Crawl4AI 执行错误，使用基础爬取替代: {}", e);
                Self::execute_basic_crawl(target)
            }
        }
    }

    /// 结果分析器
    fn execute_result_analyzer(target: &str) -> Result<String, String> {
        info!("执行结果分析: {}", target);

        // 模拟结果分析过程
        let analysis_result = json!({
            "target": target,
            "analysis_timestamp": chrono::Utc::now().to_rfc3339(),
            "summary": {
                "total_steps_completed": 8,
                "vulnerabilities_found": 0,
                "risk_level": "LOW",
                "recommendations": [
                    "定期更新系统和应用程序",
                    "启用防火墙和入侵检测系统",
                    "实施强密码策略",
                    "定期进行安全审计"
                ]
            },
            "detailed_findings": {
                "open_ports": [],
                "subdomains": [],
                "web_technologies": [],
                "security_headers": {},
                "vulnerabilities": []
            },
            "report_generated": true,
            "report_formats": ["json", "html", "pdf"]
        });

        Ok(format!("结果分析完成:\n{}", serde_json::to_string_pretty(&analysis_result).unwrap()))
    }

    /// 基础爬取 (Crawl4AI 的备用方案)
    fn execute_basic_crawl(target: &str) -> Result<String, String> {
        let url = if target.starts_with("http") {
            target.to_string()
        } else {
            format!("http://{}", target)
        };

        let result = Command::new("curl")
            .arg("-s")
            .arg("-L")
            .arg("--max-time")
            .arg("30")
            .arg("--user-agent")
            .arg("Mozilla/5.0 (compatible; SecurityScanner/1.0)")
            .arg(&url)
            .output();

        match result {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let content_length = stdout.len();

                // 简单的链接提取
                let link_count = stdout.matches("href=").count();
                let form_count = stdout.matches("<form").count();
                let script_count = stdout.matches("<script").count();

                let crawl_result = json!({
                    "url": url,
                    "content_length": content_length,
                    "links_found": link_count,
                    "forms_found": form_count,
                    "scripts_found": script_count,
                    "title": extract_title(&stdout),
                    "success": true
                });

                Ok(format!("基础爬取完成:\n{}", serde_json::to_string_pretty(&crawl_result).unwrap()))
            }
            Err(e) => Err(format!("基础爬取失败: {}", e)),
        }
    }
}

/// 辅助函数：从 HTML 内容中提取标题
fn extract_title(html: &str) -> String {
    if let Some(start) = html.find("<title>") {
        if let Some(end) = html[start + 7..].find("</title>") {
            return html[start + 7..start + 7 + end].trim().to_string();
        }
    }
    "未找到标题".to_string()
}

/// 工作流模板
impl WorkflowDefinition {
    /// 基础端口扫描工作流
    pub fn basic_port_scan() -> Self {
        let mut global_config = HashMap::new();
        global_config.insert("timeout".to_string(), json!(5000));
        global_config.insert("threads".to_string(), json!(100));

        Self {
            id: "basic_port_scan".to_string(),
            name: "基础端口扫描".to_string(),
            description: "使用 RustScan 进行快速端口扫描".to_string(),
            version: "1.0.0".to_string(),
            steps: vec![
                WorkflowStep {
                    id: "port_scan".to_string(),
                    name: "端口扫描".to_string(),
                    tool: "rustscan".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("ports".to_string(), json!("1-65535"));
                        params.insert("timeout".to_string(), json!(3000));
                        params
                    },
                    depends_on: vec![],
                },
            ],
            global_config: Some(global_config),
        }
    }

    /// 综合扫描工作流 - 按照文档流程执行
    pub fn comprehensive_scan() -> Self {
        let mut global_config = HashMap::new();
        global_config.insert("timeout".to_string(), json!(10000));
        global_config.insert("threads".to_string(), json!(50));

        Self {
            id: "comprehensive_scan".to_string(),
            name: "综合安全扫描".to_string(),
            description: "按照文档流程：目标验证 → 端口扫描 → DNS解析 → 子域名枚举 → Web资产探测 → 内容爬取 → 漏洞扫描 → 结果分析".to_string(),
            version: "2.0.0".to_string(),
            steps: vec![
                // 步骤1: 目标验证
                WorkflowStep {
                    id: "target_validation".to_string(),
                    name: "目标验证".to_string(),
                    tool: "target_validator".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("validate_dns".to_string(), json!(true));
                        params.insert("validate_connectivity".to_string(), json!(true));
                        params
                    },
                    depends_on: vec![],
                },
                // 步骤2: 端口扫描 (RustScan)
                WorkflowStep {
                    id: "port_scan".to_string(),
                    name: "端口扫描".to_string(),
                    tool: "rustscan".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("ports".to_string(), json!("1-65535"));
                        params.insert("timeout".to_string(), json!(3000));
                        params.insert("batch_size".to_string(), json!(5000));
                        params
                    },
                    depends_on: vec!["target_validation".to_string()],
                },
                // 步骤3: DNS解析 (DNSx)
                WorkflowStep {
                    id: "dns_resolution".to_string(),
                    name: "DNS解析".to_string(),
                    tool: "dnsx".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("resolvers".to_string(), json!("8.8.8.8,1.1.1.1,208.67.222.222"));
                        params.insert("record_types".to_string(), json!("A,AAAA,CNAME,MX,TXT"));
                        params
                    },
                    depends_on: vec!["port_scan".to_string()],
                },
                // 步骤4: 子域名枚举 (Subfinder)
                WorkflowStep {
                    id: "subdomain_discovery".to_string(),
                    name: "子域名枚举".to_string(),
                    tool: "subfinder".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("sources".to_string(), json!("all"));
                        params.insert("timeout".to_string(), json!(30));
                        params.insert("max_enumerating_time".to_string(), json!(300));
                        params
                    },
                    depends_on: vec!["dns_resolution".to_string()],
                },
                // 步骤5: Web资产探测 (HTTPx)
                WorkflowStep {
                    id: "web_asset_discovery".to_string(),
                    name: "Web资产探测".to_string(),
                    tool: "httpx".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("threads".to_string(), json!(50));
                        params.insert("timeout".to_string(), json!(10));
                        params.insert("follow_redirects".to_string(), json!(true));
                        params.insert("tech_detect".to_string(), json!(true));
                        params
                    },
                    depends_on: vec!["subdomain_discovery".to_string()],
                },
                // 步骤6: 内容爬取 (Crawl4AI)
                WorkflowStep {
                    id: "content_crawling".to_string(),
                    name: "内容爬取".to_string(),
                    tool: "crawl4ai".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("max_depth".to_string(), json!(3));
                        params.insert("max_pages".to_string(), json!(100));
                        params.insert("extract_links".to_string(), json!(true));
                        params.insert("extract_forms".to_string(), json!(true));
                        params.insert("extract_js".to_string(), json!(true));
                        params
                    },
                    depends_on: vec!["web_asset_discovery".to_string()],
                },
                // 步骤7: 漏洞扫描 (Nuclei)
                WorkflowStep {
                    id: "vulnerability_scan".to_string(),
                    name: "漏洞扫描".to_string(),
                    tool: "nuclei".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("templates".to_string(), json!("cves,vulnerabilities,exposures,misconfiguration"));
                        params.insert("severity".to_string(), json!("medium,high,critical"));
                        params.insert("rate_limit".to_string(), json!(150));
                        params.insert("bulk_size".to_string(), json!(25));
                        params
                    },
                    depends_on: vec!["content_crawling".to_string()],
                },
                // 步骤8: 结果分析
                WorkflowStep {
                    id: "result_analysis".to_string(),
                    name: "结果分析".to_string(),
                    tool: "result_analyzer".to_string(),
                    parameters: {
                        let mut params = HashMap::new();
                        params.insert("generate_report".to_string(), json!(true));
                        params.insert("report_format".to_string(), json!("json,html,pdf"));
                        params.insert("risk_assessment".to_string(), json!(true));
                        params.insert("recommendations".to_string(), json!(true));
                        params
                    },
                    depends_on: vec!["vulnerability_scan".to_string()],
                },
            ],
            global_config: Some(global_config),
        }
    }
}

/// 健康检查端点
async fn health_check() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(json!({
        "status": "healthy",
        "service": "RustScan 工作流协调器演示",
        "version": "2.4.1",
        "features": ["workflow_coordinator", "tool_management"],
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

/// 获取工作流模板列表
async fn get_workflow_templates() -> Result<HttpResponse> {
    let basic_scan = WorkflowDefinition::basic_port_scan();
    let comprehensive_scan = WorkflowDefinition::comprehensive_scan();
    
    let templates = vec![basic_scan, comprehensive_scan];
    
    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": {
            "templates": templates,
            "count": templates.len()
        }
    })))
}

/// 创建工作流实例
async fn create_workflow_instance(
    req: web::Json<CreateWorkflowInstanceRequest>,
    workflow_manager: web::Data<WorkflowManager>,
) -> Result<HttpResponse> {
    info!("创建工作流实例: {} - {}", req.name, req.target);

    match workflow_manager.create_instance(req.into_inner()) {
        Ok(instance) => {
            info!("工作流实例创建成功: {}", instance.instance_id);
            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "data": {
                    "instance_id": instance.instance_id,
                    "workflow_id": instance.workflow_id,
                    "name": instance.name,
                    "target": instance.target,
                    "status": instance.status,
                    "created_at": instance.created_at,
                    "message": "工作流实例创建成功，开始执行扫描"
                }
            })))
        }
        Err(error) => {
            error!("工作流实例创建失败: {}", error);
            Ok(HttpResponse::BadRequest().json(json!({
                "success": false,
                "error": {
                    "code": "CREATE_INSTANCE_FAILED",
                    "message": error
                }
            })))
        }
    }
}

/// 获取所有工作流实例
async fn get_workflow_instances(
    workflow_manager: web::Data<WorkflowManager>,
) -> Result<HttpResponse> {
    let instances: Vec<_> = workflow_manager.get_all_instances()
        .into_iter()
        .map(|instance| json!({
            "instance_id": instance.instance_id,
            "name": instance.name,
            "workflow_id": instance.workflow_id,
            "target": instance.target,
            "status": instance.status,
            "current_step": instance.current_step,
            "progress": instance.progress,
            "created_at": instance.created_at,
            "started_at": instance.started_at,
            "completed_at": instance.completed_at
        }))
        .collect();

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": {
            "instances": instances,
            "count": instances.len()
        }
    })))
}

/// 获取工作流执行状态
async fn get_workflow_status(
    path: web::Path<String>,
    workflow_manager: web::Data<WorkflowManager>,
) -> Result<HttpResponse> {
    let instance_id = path.into_inner();

    match workflow_manager.get_instance(&instance_id) {
        Some(instance) => {
            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "data": {
                    "instance_id": instance.instance_id,
                    "status": instance.status,
                    "current_step": instance.current_step,
                    "progress": instance.progress,
                    "steps": instance.steps,
                    "created_at": instance.created_at,
                    "started_at": instance.started_at,
                    "completed_at": instance.completed_at
                }
            })))
        }
        None => {
            Ok(HttpResponse::NotFound().json(json!({
                "success": false,
                "error": {
                    "code": "INSTANCE_NOT_FOUND",
                    "message": format!("工作流实例不存在: {}", instance_id)
                }
            })))
        }
    }
}

/// 获取工作流统计信息
async fn get_workflow_statistics(
    workflow_manager: web::Data<WorkflowManager>,
) -> Result<HttpResponse> {
    let instances = workflow_manager.instances.lock().unwrap();

    let mut running = 0;
    let mut completed = 0;
    let mut failed = 0;
    let mut created = 0;

    for instance in instances.values() {
        match instance.status {
            InstanceStatus::Running => running += 1,
            InstanceStatus::Completed => completed += 1,
            InstanceStatus::Failed => failed += 1,
            InstanceStatus::Created => created += 1,
            _ => {}
        }
    }

    let total = instances.len();
    let success_rate = if total > 0 {
        completed as f64 / total as f64
    } else {
        0.0
    };

    let stats = json!({
        "templates": {
            "total": 2,
            "available": ["basic_port_scan", "comprehensive_scan"]
        },
        "instances": {
            "total": total,
            "running": running,
            "completed": completed,
            "failed": failed,
            "pending": created
        },
        "tools": {
            "available": ["target_validator", "rustscan", "nmap", "dnsx", "subfinder", "httpx", "crawl4ai", "nuclei", "result_analyzer"],
            "total_executions": total,
            "success_rate": success_rate
        }
    });

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": stats
    })))
}

/// 配置应用路由
fn configure_app(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/api/v1")
            .route("/health", web::get().to(health_check))
            .route("/workflow-templates", web::get().to(get_workflow_templates))
            .route("/workflow-instances", web::get().to(get_workflow_instances))
            .route("/workflow-instances", web::post().to(create_workflow_instance))
            .route("/workflow-instances/{id}/status", web::get().to(get_workflow_status))
            .route("/workflow-statistics", web::get().to(get_workflow_statistics)),
    );
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_target(false)
        .init();

    info!("🚀 启动 RustScan 工作流协调器演示服务");

    let host = "127.0.0.1";
    let port = 8080;

    // 初始化工作流管理器
    let workflow_manager = web::Data::new(WorkflowManager::new());
    info!("📋 工作流管理器初始化完成");

    info!("🌐 服务器启动在 http://{}:{}", host, port);
    info!("📖 健康检查: http://{}:{}/api/v1/health", host, port);
    info!("📋 工作流模板: http://{}:{}/api/v1/workflow-templates", host, port);
    info!("📊 工作流统计: http://{}:{}/api/v1/workflow-statistics", host, port);
    info!("⚡ 真实扫描功能已启用");

    // 启动 HTTP 服务器
    HttpServer::new(move || {
        // 配置 CORS
        let cors = Cors::default()
            .allow_any_origin()
            .allow_any_method()
            .allow_any_header()
            .max_age(3600);

        App::new()
            .app_data(workflow_manager.clone())
            .wrap(cors)
            .wrap(Logger::default())
            .configure(configure_app)
    })
    .bind(format!("{}:{}", host, port))?
    .run()
    .await
}
