# The configurations that used for the recording, feel free to edit them
config:

  # Specify a command to be executed
  # like `/bin/bash -l`, `ls`, or any other commands
  # the default is bash for Linux
  # or powershell.exe for Windows
  command: zsh 
  
  # Specify the current working directory path
  # the default is the current working directory path
  cwd: /home/<USER>/Documents/RustScan
  
  # Export additional ENV variables
  env:
    recording: true
  
  # Explicitly set the number of columns
  # or use `auto` to take the current
  # number of columns of your shell
  cols: 235
  
  # Explicitly set the number of rows
  # or use `auto` to take the current
  # number of rows of your shell
  rows: 31
  
  # Amount of times to repeat GIF
  # If value is -1, play once
  # If value is 0, loop indefinitely
  # If value is a positive number, loop n times
  repeat: 0
  
  # Quality
  # 1 - 100
  quality: 100
  
  # Delay between frames in ms
  # If the value is `auto` use the actual recording delays
  frameDelay: auto
  
  # Maximum delay between frames in ms
  # Ignored if the `frameDelay` isn't set to `auto`
  # Set to `auto` to prevent limiting the max idle time
  maxIdleTime: auto
  
  # The surrounding frame box
  # The `type` can be null, window, floating, or solid`
  # To hide the title use the value null
  # Don't forget to add a backgroundColor style with a null as type
  frameBox:
    type: floating
    title: Terminalizer
    style:
      border: 0px black solid
      # boxShadow: none
      # margin: 0px
  
  # Add a watermark image to the rendered gif
  # You need to specify an absolute path for
  # the image on your machine or a URL, and you can also
  # add your own CSS styles
  watermark:
    imagePath: null
    style:
      position: absolute
      right: 15px
      bottom: 15px
      width: 100px
      opacity: 0.9
  
  # Cursor style can be one of
  # `block`, `underline`, or `bar`
  cursorStyle: block
  
  # Font family
  # You can use any font that is installed on your machine
  # in CSS-like syntax
  fontFamily: "Monaco, Lucida Console, Ubuntu Mono, Monospace"
  
  # The size of the font
  fontSize: 12
  
  # The height of lines
  lineHeight: 1
  
  # The spacing between letters
  letterSpacing: 0
  
  # Theme
  theme:
    background: "transparent"
    foreground: "#afafaf"
    cursor: "#c7c7c7"
    black: "#232628"
    red: "#fc4384"
    green: "#b3e33b"
    yellow: "#ffa727"
    blue: "#75dff2"
    magenta: "#ae89fe"
    cyan: "#708387"
    white: "#d5d5d0"
    brightBlack: "#626566"
    brightRed: "#ff7fac"
    brightGreen: "#c8ed71"
    brightYellow: "#ebdf86"
    brightBlue: "#75dff2"
    brightMagenta: "#ae89fe"
    brightCyan: "#b1c6ca"
    brightWhite: "#f9f9f4"
  
# Records, feel free to edit them
records:
  - delay: 818
    content: "\e[1m\e[7m%\e[27m\e[1m\e[0m                                                                                                                                                                                                                                          \r \r\e]2;bee@beehive: ~/Documents/RustScan\a\e]1;..ents/RustScan\a"
  - delay: 187
    content: "\r\e[0m\e[27m\e[24m\e[J\r\n\e[1m\e[0m\e[1m\e[36mRustScan\e[0m\e[36m\e[39m\e[1m \e[0m\e[1mon \e[0m\e[1m\e[37m\e[1m\e[37m\e[0m\e[37m\e[1m\e[37m\e[35m master\e[0m\e[35m\e[39m\e[1m\e[0m\e[1m\e[0m\e[1m\e[31m [⇡$!?]\e[0m\e[31m\e[39m\e[1m\e[0m\e[0m\e[39m\e[1m \e[0m\e[1mis \e[0m\e[1m\e[31m\U0001F4E6 vrustscan:1.0.1\e[0m\e[31m\e[39m\e[1m \e[0m\e[1mvia \e[0m\e[1m\e[31m\U0001D5E5 v1.45.0\e[0m\e[31m\e[39m\e[1m \e[0m\r\n\e[1m\e[0m\e[1m\e[32m➜ \e[0m\e[32m\e[39m\e[1m\e[0m\e[K\e[?1h\e=\e[?2004h"
  - delay: 432
    content: "\e[32mc\e[39m\e[90margo build --release && ./target/release/rustscan 8.8.8.8\e[39m\e[57D"
  - delay: 106
    content: "\b\e[1m\e[31mc\e[1m\e[31ma\e[0m\e[39m"
  - delay: 115
    content: "\b\b\e[1m\e[31mc\e[1m\e[31ma\e[1m\e[31mr\e[0m\e[39m"
  - delay: 178
    content: "\b\b\b\e[0m\e[32mc\e[0m\e[32ma\e[0m\e[32mr\e[32mg\e[32mo\e[39m\e[39m \e[39mb\e[39mu\e[39mi\e[39ml\e[39md\e[39m \e[39m-\e[39m-\e[39mr\e[39me\e[39ml\e[39me\e[39ma\e[39ms\e[39me\e[39m \e[39m&\e[39m&\e[39m \e[32m.\e[32m/\e[32mt\e[32ma\e[32mr\e[32mg\e[32me\e[32mt\e[32m/\e[32mr\e[32me\e[32ml\e[32me\e[32ma\e[32ms\e[32me\e[32m/\e[32mr\e[32mu\e[32ms\e[32mt\e[32ms\e[32mc\e[32ma\e[32mn\e[39m\e[39m \e[39m8\e[39m.\e[39m8\e[39m.\e[39m8\e[39m.\e[39m8"
  - delay: 143
    content: "\e[?1l\e>"
  - delay: 8
    content: "\e[?2004l\r\r\n\e]2;cargo build --release && ./target/release/rustscan 8.8.8.8\a\e]1;cargo\a"
  - delay: 40
    content: "\e[0m\e[0m\e[1m\e[36m    Building\e[0m [=================================================>       ] 82/92: proc-macro-nested(build), rayon-core                                                                                                                     \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [==================================================>      ] 83/92: rayon, proc-macro-nested(build)                                                                                                                          \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [===================================================>     ] 84/92: proc-macro-nested(build)                                                                                                                                 \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [===================================================>     ] 85/92: proc-macro-nested                                                                                                                                        \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [====================================================>    ] 86/92: futures-util                                                                                                                                             \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [====================================================>    ] 87/92: smol, futures-executor                                                                                                                                   \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [======================================================>  ] 89/92: async-std, futures                                                                                                                                       \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [======================================================>  ] 90/92: async-std                                                                                                                                                \r\e[0m\e[0m\e[1m\e[36m    Building\e[0m [=======================================================> ] 91/92: rustscan(bin)                                                                                                                                            \r\e[K\e[0m\e[1m\e[33mwarning\e[0m\e[0m\e[1m: unused import: `Command`\e[0m\r\n\e[0m \e[0m\e[0m\e[1m\e[38;5;12m--> \e[0m\e[0msrc/main.rs:5:26\e[0m\r\n\e[0m  \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m\e[1m\e[38;5;12m5\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0muse std::process::{exit, Command};\e[0m\r\n\e[0m  \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m                         \e[0m\e[0m\e[1m\e[33m^^^^^^^\e[0m\r\n\e[0m  \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m  \e[0m\e[0m\e[1m\e[38;5;12m= \e[0m\e[0m\e[1mnote\e[0m\e[0m: `#[warn(unused_imports)]` on by default\e[0m\r\n\r\n\e[0m\e[0m\e[1m\e[36m    Building\e[0m [=======================================================> ] 91/92: rustscan(bin)                                                                                                                                            \r\e[K\e[0m\e[1m\e[33mwarning\e[0m\e[0m\e[1m: unreachable expression\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m--> \e[0m\e[0msrc/main.rs:181:21\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m\e[1m\e[38;5;12m180\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m                    panic!(\"Too many open files. Please reduce batch size.\");\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m                    \e[0m\e[0m\e[1m\e[38;5;12m---------------------------------------------------------\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12many code following this expression is unreachable\e[0m\r\n\e[0m\e[1m\e[38;5;12m181\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m                    Err(e)\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m                    \e[0m\e[0m\e[1m\e[33m^^^^^^\e[0m\e[0m \e[0m\e[0m\e[1m\e[33munreachable expression\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m= \e[0m\e[0m\e[1mnote\e[0m\e[0m: `#[warn(unreachable_code)]` on by default\e[0m\r\n\r\n\e[0m\e[1m\e[33mwarning\e[0m\e[0m\e[1m: unreachable expression\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m--> \e[0m\e[0msrc/main.rs:189:13\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m\e[1m\e[38;5;12m188\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m            panic!(\"Unable to convert to socket address\");\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m            \e[0m\e[0m\e[1m\e[38;5;12m----------------------------------------------\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12many code following this expression is unreachable\e[0m\r\n\e[0m\e[1m\e[38;5;12m189\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m            Err(io::Error::new(io::ErrorKind::Other, e.to_string()))\e[0m\r\n\e[0m    \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m            \e[0m\e[0m\e[1m\e[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\e[0m\e[0m \e[0m\e[0m\e[1m\e[33munreachable expression\e[0m\r\n\r\n\e[0m\e[1m\e[33mwarning\e[0m\e[0m\e[1m: unused variable: `command_run`\e[0m\r\n\e[0m  \e[0m\e[0m\e[1m\e[38;5;12m--> \e[0m\e[0msrc/main.rs:55:9\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m\e[1m\e[38;5;12m55\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m    let command_run: String = match command_matches {\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m        \e[0m\e[0m\e[1m\e[33m^^^^^^^^^^^\e[0m\e[0m \e[0m\e[0m\e[1m\e[33mhelp: if this is intentional, prefix it with an underscore: `_command_run`\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m= \e[0m\e[0m\e[1mnote\e[0m\e[0m: `#[warn(unused_variables)]` on by default\e[0m\r\n\r\n\e[0m\e[1m\e[33mwarning\e[0m\e[0m\e[1m: unused variable: `x`\e[0m\r\n\e[0m  \e[0m\e[0m\e[1m\e[38;5;12m--> \e[0m\e[0msrc/main.rs:57:14\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m|\e[0m\r\n\e[0m\e[1m\e[38;5;12m57\e[0m\e[0m \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m        Some(x) => {\e[0m\r\n\e[0m   \e[0m\e[0m\e[1m\e[38;5;12m| \e[0m\e[0m             \e[0m\e[0m\e[1m\e[33m^\e[0m\e[0m \e[0m\e[0m\e[1m\e[33mhelp: if this is intentional, prefix it with an underscore: `_x`\e[0m\r\n\r\n\e[0m\e[1m\e[33mwarning\e[0m\e[0m\e[1m: 5 warnings emitted\e[0m\r\n\r\n\e[0m\e[0m\e[1m\e[32m    Finished\e[0m release [optimized] target(s) in 0.03s\r\n\e[32m\r\n     _____           _    _____                 \r\n    |  __ \\         | |  / ____|                \r\n    | |__) |   _ ___| |_| (___   ___ __ _ _ __  \r\n    |  _  / | | / __| __|\\___ \\ / __/ _` | '_ \\ \r\n    | | \\ \\ |_| \\__ \\ |_ ____) | (_| (_| | | | |\r\n    |_|  \\_\\__,_|___/\\__|_____/ \\___\\__,_|_| |_|\r\n    Faster nmap scanning with rust.\e[0m \r\n \e[31mAutomated Decryption Tool - https://github.com/ciphey/ciphey\e[0m \r\n \e[32mCreator https://github.com/brandonskerritt\e[0m\r\n"
  - delay: 368
    content: "Open \e[35m53\e[0m\r\n"
  - delay: 37
    content: "Open \e[35m853\e[0m\r\n"
  - delay: 849
    content: "Open \e[35m443\e[0m\r\n"
  - delay: 6605
    content: "\e[34mStarting nmap.\e[0m\r\n\e[1m\e[7m%\e[27m\e[1m\e[0m                                                                                                                                                                                                                                          \r \r\e]2;bee@beehive: ~/Documents/RustScan\a\e]1;..ents/RustScan\a"
  - delay: 175
    content: "\r\e[0m\e[27m\e[24m\e[J\r\n\e[1m\e[0m\e[1m\e[36mRustScan\e[0m\e[36m\e[39m\e[1m \e[0m\e[1mon \e[0m\e[1m\e[37m\e[1m\e[37m\e[0m\e[37m\e[1m\e[37m\e[35m master\e[0m\e[35m\e[39m\e[1m\e[0m\e[1m\e[0m\e[1m\e[31m [⇡$!?]\e[0m\e[31m\e[39m\e[1m\e[0m\e[0m\e[39m\e[1m \e[0m\e[1mis \e[0m\e[1m\e[31m\U0001F4E6 vrustscan:1.0.1\e[0m\e[31m\e[39m\e[1m \e[0m\e[1mvia \e[0m\e[1m\e[31m\U0001D5E5 v1.45.0\e[0m\e[31m\e[39m\e[1m \e[0m\e[1mtook \e[0m\e[1m\e[33m8s\e[0m\e[33m\e[39m\e[1m \e[0m\r\n\e[1m\e[0m\e[1m\e[31m➜ \e[0m\e[31m\e[39m\e[1m\e[0m\e[K\e[?1h\e=\e[?2004h"
  - delay: 554
    content: "\e[?2004l\r\r\n"
