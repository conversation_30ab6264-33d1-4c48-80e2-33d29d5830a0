# RustScan 工作流协调器演示

这是一个完整的 RustScan 工作流协调器演示系统，包含后端 API 服务和现代化的 React 前端界面。

## 🚀 功能特性

### 后端 API 服务
- **工作流模板管理**: 预定义的扫描工作流模板
- **工作流实例管理**: 创建、监控和管理工作流执行
- **实时状态监控**: 工作流执行进度和状态跟踪
- **统计信息**: 系统使用统计和性能指标
- **RESTful API**: 完整的 REST API 接口

### 前端 Web 界面
- **现代化 UI**: 基于 React + TypeScript + Tailwind CSS
- **响应式设计**: 支持桌面和移动设备
- **实时监控**: 工作流执行状态实时更新
- **直观操作**: 简单易用的工作流创建和管理界面
- **数据可视化**: 统计图表和进度展示

## 📋 预定义工作流模板

### 1. 基础端口扫描
- **工具**: RustScan
- **功能**: 快速端口扫描
- **适用场景**: 基础网络发现

### 2. 综合安全扫描
- **步骤流程**:
  1. 子域名发现 (Subfinder)
  2. DNS 解析 (DNSx)
  3. 端口扫描 (RustScan)
  4. Web 服务探测 (HTTPx)
  5. 漏洞扫描 (Nuclei)
- **适用场景**: 全面的安全评估

## 🛠️ 技术栈

### 后端
- **语言**: Rust
- **框架**: Actix-web
- **特性**: 异步处理、高性能、内存安全

### 前端
- **语言**: TypeScript
- **框架**: React 18
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **路由**: React Router
- **HTTP 客户端**: Axios

## 🚀 快速开始

### 启动后端服务

```bash
# 进入工作流演示目录
cd workflow_demo

# 启动后端 API 服务
RUST_LOG=info cargo run
```

后端服务将在 `http://127.0.0.1:8080` 启动

### 启动前端应用

```bash
# 进入前端目录
cd rustscan-frontend

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm start
```

前端应用将在 `http://localhost:3000` 启动

## 📖 API 文档

### 主要端点

- `GET /api/v1/health` - 健康检查
- `GET /api/v1/workflow-templates` - 获取工作流模板
- `POST /api/v1/workflow-instances` - 创建工作流实例
- `GET /api/v1/workflow-instances/{id}/status` - 获取工作流状态
- `GET /api/v1/workflow-statistics` - 获取统计信息

### 示例请求

```bash
# 获取工作流模板
curl http://127.0.0.1:8080/api/v1/workflow-templates

# 创建工作流实例
curl -X POST http://127.0.0.1:8080/api/v1/workflow-instances \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_id": "basic_port_scan",
    "name": "测试扫描",
    "target": "example.com"
  }'

# 获取统计信息
curl http://127.0.0.1:8080/api/v1/workflow-statistics
```

## 🎯 使用指南

### 1. 访问仪表板
- 打开 `http://localhost:3000`
- 查看系统概览和统计信息

### 2. 浏览工作流模板
- 点击"工作流模板"查看可用模板
- 查看模板详情和执行步骤

### 3. 创建工作流实例
- 点击"创建工作流"
- 选择模板并配置目标
- 启动工作流执行

### 4. 监控执行状态
- 在"工作流实例"中查看所有实例
- 点击实例查看详细执行状态
- 实时监控执行进度

## 🔧 配置说明

### 后端配置
- 服务地址: `127.0.0.1:8080`
- 日志级别: `RUST_LOG=info`
- CORS: 允许所有来源

### 前端配置
- 开发服务器: `localhost:3000`
- API 代理: 自动代理到后端服务
- 自动刷新: 支持热重载

## 📊 系统架构

```
┌─────────────────┐    HTTP/REST    ┌─────────────────┐
│   React 前端    │ ──────────────► │   Rust 后端     │
│                 │                 │                 │
│ - 用户界面      │                 │ - API 服务      │
│ - 状态管理      │                 │ - 工作流引擎    │
│ - 实时更新      │                 │ - 任务调度      │
└─────────────────┘                 └─────────────────┘
```

## 🎨 界面预览

- **仪表板**: 系统概览和快速操作
- **模板管理**: 工作流模板浏览和详情
- **实例管理**: 工作流实例列表和状态
- **创建向导**: 简单的工作流创建流程
- **详情监控**: 实时的执行状态和进度

## 🔍 演示数据

系统包含模拟数据用于演示：
- 2 个预定义工作流模板
- 模拟的工作流实例和执行状态
- 统计数据和性能指标

## 📝 注意事项

1. 这是一个演示系统，不包含实际的工具执行
2. 工作流状态和进度是模拟数据
3. 适用于概念验证和界面演示
4. 可以作为实际系统开发的基础框架

## 🚀 扩展建议

1. **实际工具集成**: 集成真实的安全扫描工具
2. **数据持久化**: 添加数据库支持
3. **用户认证**: 实现用户管理和权限控制
4. **结果存储**: 保存和展示扫描结果
5. **通知系统**: 工作流完成通知
6. **调度系统**: 定时任务和批量处理

---

🎉 **恭喜！** 您已经成功部署了 RustScan 工作流协调器演示系统！
