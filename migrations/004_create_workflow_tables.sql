-- 创建工作流相关表

-- 工作流定义表
CREATE TABLE workflow_definitions (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT NOT NULL DEFAULT '1.0.0',
    definition_json TEXT NOT NULL, -- JSON格式的工作流定义
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 工作流实例表
CREATE TABLE workflow_instances (
    id TEXT PRIMARY KEY,
    workflow_id TEXT NOT NULL,
    name TEXT NOT NULL,
    target TEXT NOT NULL, -- 扫描目标
    status TEXT NOT NULL DEFAULT 'pending', -- pending, running, completed, failed, cancelled
    current_step TEXT, -- 当前执行的步骤ID
    context TEXT, -- JSON格式的执行上下文
    error_message TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (workflow_id) REFERENCES workflow_definitions(id) ON DELETE CASCADE
);

-- 工作流步骤实例表
CREATE TABLE workflow_step_instances (
    id TEXT PRIMARY KEY,
    workflow_instance_id TEXT NOT NULL,
    step_id TEXT NOT NULL, -- 对应工作流定义中的步骤ID
    name TEXT NOT NULL,
    tool TEXT NOT NULL, -- 使用的工具名称
    status TEXT NOT NULL DEFAULT 'pending', -- pending, running, completed, failed, skipped
    input_data TEXT, -- JSON格式的输入数据
    output_data TEXT, -- JSON格式的输出数据
    error_message TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    FOREIGN KEY (workflow_instance_id) REFERENCES workflow_instances(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_workflow_definitions_name ON workflow_definitions(name);
CREATE INDEX idx_workflow_definitions_created_at ON workflow_definitions(created_at);

CREATE INDEX idx_workflow_instances_workflow_id ON workflow_instances(workflow_id);
CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX idx_workflow_instances_target ON workflow_instances(target);
CREATE INDEX idx_workflow_instances_created_at ON workflow_instances(created_at);

CREATE INDEX idx_workflow_step_instances_workflow_instance_id ON workflow_step_instances(workflow_instance_id);
CREATE INDEX idx_workflow_step_instances_step_id ON workflow_step_instances(step_id);
CREATE INDEX idx_workflow_step_instances_status ON workflow_step_instances(status);
CREATE INDEX idx_workflow_step_instances_tool ON workflow_step_instances(tool);
CREATE INDEX idx_workflow_step_instances_created_at ON workflow_step_instances(created_at);
