-- 初始数据库结构
-- 创建扫描任务表
CREATE TABLE IF NOT EXISTS tasks (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    target TEXT NOT NULL,
    scan_type TEXT NOT NULL DEFAULT 'standard',
    status TEXT NOT NULL DEFAULT 'pending',
    config TEXT, -- JSON 配置
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- 创建扫描目标表
CREATE TABLE IF NOT EXISTS targets (
    id TEXT PRIMARY KEY,
    task_id TEXT NOT NULL,
    target TEXT NOT NULL,
    target_type TEXT NOT NULL, -- ip, domain, url
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- 创建扫描结果表
CREATE TABLE IF NOT EXISTS scan_results (
    id TEXT PRIMARY KEY,
    task_id TEXT NOT NULL,
    target_id TEXT NOT NULL,
    tool TEXT NOT NULL, -- rustscan, nmap, dnsx, etc.
    result_type TEXT NOT NULL, -- port, subdomain, vulnerability, etc.
    data TEXT NOT NULL, -- JSON 数据
    severity TEXT, -- low, medium, high, critical
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (target_id) REFERENCES targets(id) ON DELETE CASCADE
);

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS configurations (
    id TEXT PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_targets_task_id ON targets(task_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_task_id ON scan_results(task_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_target_id ON scan_results(target_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_tool ON scan_results(tool);
CREATE INDEX IF NOT EXISTS idx_scan_results_severity ON scan_results(severity);

-- 插入默认配置
INSERT OR IGNORE INTO configurations (id, key, value, description) VALUES
    ('default_timeout', 'default_timeout', '5000', '默认超时时间（毫秒）'),
    ('max_concurrent_tasks', 'max_concurrent_tasks', '5', '最大并发任务数'),
    ('default_batch_size', 'default_batch_size', '1000', '默认批处理大小'),
    ('enable_logging', 'enable_logging', 'true', '是否启用日志记录');
