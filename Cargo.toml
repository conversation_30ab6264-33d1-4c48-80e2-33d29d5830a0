[package]
name = "rustscan"
version = "2.4.1"
authors = ["Autumn <<EMAIL>>"]
edition = "2018"
description = "Faster Nmap Scanning with Rust"
homepage = "https://github.com/rustscan/rustscan"
repository = "https://github.com/rustscan/rustscan"
license = "GPL-3.0-only"
keywords = ["port", "scanning", "nmap"]
categories = ["command-line-utilities"]
readme = "README.md"
exclude = [
    ".github/*",
    "pictures/*",
    "rustscan-debbuilder/*",
]

[dependencies]
clap = { version = "4.5.41", features = ["derive", "wrap_help"] }
colored = "3.0.0"
async-std = "1.13.1"
futures = "0.3"
rlimit = "0.10.2"
log = "0.4.27"
env_logger = "0.11.8"
anstream = "=0.6.19"
dirs = "6.0.0"
gcd = "2.0.1"
rand = "0.9.1"
colorful = "0.3.2"
ansi_term = "0.12.1"
toml = "0.9.0"
serde = "1.0.124"
serde_derive = "1.0.116"
cidr-utils = "0.6.1"
itertools = "0.14.0"
hickory-resolver = { version = "0.24.3", features = ["dns-over-rustls"] }
anyhow = "1.0.40"
text_placeholder = { version = "0.5", features = ["struct_context"] }
once_cell = "1.21.3"

# Web 服务相关依赖
actix-web = "4.9.0"
actix-cors = "0.7.0"
actix-files = "0.6.6"
actix-ws = "0.3.0"
tokio = { version = "1.40.0", features = ["full"] }
sqlx = { version = "0.8.2", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid", "migrate"] }
serde_json = "1.0.132"
chrono = { version = "0.4.38", features = ["serde"] }
uuid = { version = "1.11.0", features = ["v4", "serde"] }
thiserror = "2.0.3"
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
tracing-actix-web = "0.7.13"
async-trait = "0.1.83"

[features]
default = []
web = []

[dev-dependencies]
parameterized = "2.0.0"
wait-timeout = "0.2"
criterion = { version = "0.6", features = ["html_reports"] }

[package.metadata.deb]
depends = "$auto, nmap"
section = "rust"

[profile.release]
lto = true
panic = 'abort'
strip = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[[bin]]
name = "rustscan"
path = "src/main.rs"

[[bin]]
name = "rustscan-web"
path = "src/web/main.rs"

[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = ["cfg(tarpaulin_include)"] }

[[bench]]
name = "benchmark_portscan"
harness = false
