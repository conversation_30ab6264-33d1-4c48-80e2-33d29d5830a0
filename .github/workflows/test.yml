name: Test
on:
  pull_request:
    branches: [master]
  push:
    branches:
      - master
jobs:
  test:
    name: Test Suite
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        rust: [stable]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Install stable toolchain
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: ${{ matrix.rust }}
          override: true

      - uses: taiki-e/install-action@nextest
      - uses: Swatinem/rust-cache@v2

      - name: Install Just
        uses: extractions/setup-just@v2

      - name: Run just
        run: just test
