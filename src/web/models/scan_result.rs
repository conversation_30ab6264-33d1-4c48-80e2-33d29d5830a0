//! 扫描结果数据模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::convert::TryFrom;

/// 扫描结果类型
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ResultType {
    #[sqlx(rename = "port")]
    Port,
    #[sqlx(rename = "subdomain")]
    Subdomain,
    #[sqlx(rename = "vulnerability")]
    Vulnerability,
    #[sqlx(rename = "service")]
    Service,
    #[sqlx(rename = "technology")]
    Technology,
}

/// 严重程度
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum Severity {
    #[sqlx(rename = "info")]
    Info,
    #[sqlx(rename = "low")]
    Low,
    #[sqlx(rename = "medium")]
    Medium,
    #[sqlx(rename = "high")]
    High,
    #[sqlx(rename = "critical")]
    Critical,
}

/// 扫描结果实体
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ScanResult {
    pub id: String,
    pub task_id: String,
    pub target_id: String,
    pub tool: String,
    pub result_type: ResultType,
    pub data: String, // JSON 数据
    pub severity: Option<Severity>,
    pub created_at: DateTime<Utc>,
}

/// 扫描结果响应
#[derive(Debug, Serialize)]
pub struct ScanResultResponse {
    pub id: String,
    pub task_id: String,
    pub target_id: String,
    pub tool: String,
    pub result_type: ResultType,
    pub data: serde_json::Value,
    pub severity: Option<Severity>,
    pub created_at: DateTime<Utc>,
}

impl TryFrom<ScanResult> for ScanResultResponse {
    type Error = serde_json::Error;

    fn try_from(result: ScanResult) -> Result<Self, Self::Error> {
        Ok(Self {
            id: result.id,
            task_id: result.task_id,
            target_id: result.target_id,
            tool: result.tool,
            result_type: result.result_type,
            data: serde_json::from_str(&result.data)?,
            severity: result.severity,
            created_at: result.created_at,
        })
    }
}
