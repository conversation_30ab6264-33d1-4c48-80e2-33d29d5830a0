//! 扫描目标数据模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 目标类型
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum TargetType {
    #[sqlx(rename = "ip")]
    Ip,
    #[sqlx(rename = "domain")]
    Domain,
    #[sqlx(rename = "url")]
    Url,
}

/// 扫描目标实体
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Target {
    pub id: String,
    pub task_id: String,
    pub target: String,
    pub target_type: TargetType,
    pub created_at: DateTime<Utc>,
}
