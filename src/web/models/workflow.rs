//! 工作流数据模型

use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;
use uuid::Uuid;

/// 工作流状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum WorkflowStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "running")]
    Running,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "failed")]
    Failed,
    #[serde(rename = "cancelled")]
    Cancelled,
}

impl Default for WorkflowStatus {
    fn default() -> Self {
        WorkflowStatus::Pending
    }
}

/// 工作流步骤状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum StepStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "running")]
    Running,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "failed")]
    Failed,
    #[serde(rename = "skipped")]
    Skipped,
}

impl Default for StepStatus {
    fn default() -> Self {
        StepStatus::Pending
    }
}

/// 工作流步骤定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowStep {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub tool: String,                      // 使用的工具名称
    pub config: Option<serde_json::Value>, // 工具配置
    pub depends_on: Vec<String>,           // 依赖的步骤ID列表
    pub retry_count: u32,                  // 重试次数
    pub timeout: Option<u64>,              // 超时时间（秒）
    pub continue_on_failure: bool,         // 失败时是否继续执行后续步骤
}

/// 工作流定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowDefinition {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    pub steps: Vec<WorkflowStep>,
    pub global_config: Option<serde_json::Value>, // 全局配置
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

/// 工作流实例
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct WorkflowInstance {
    pub id: String,
    pub workflow_id: String, // 关联的工作流定义ID
    pub name: String,
    pub target: String,               // 扫描目标
    pub status: String,               // 使用字符串存储状态
    pub current_step: Option<String>, // 当前执行的步骤ID
    pub context: Option<String>,      // JSON格式的执行上下文
    pub error_message: Option<String>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub started_at: Option<NaiveDateTime>,
    pub completed_at: Option<NaiveDateTime>,
}

/// 工作流步骤实例
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct WorkflowStepInstance {
    pub id: String,
    pub workflow_instance_id: String,
    pub step_id: String, // 对应工作流定义中的步骤ID
    pub name: String,
    pub tool: String,
    pub status: String,              // 使用字符串存储状态
    pub input_data: Option<String>,  // JSON格式的输入数据
    pub output_data: Option<String>, // JSON格式的输出数据
    pub error_message: Option<String>,
    pub retry_count: i64,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub started_at: Option<NaiveDateTime>,
    pub completed_at: Option<NaiveDateTime>,
}

/// 创建工作流实例请求
#[derive(Debug, Deserialize)]
pub struct CreateWorkflowInstanceRequest {
    pub workflow_id: String,
    pub name: String,
    pub target: String,
    pub context: Option<serde_json::Value>,
}

/// 工作流执行上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowContext {
    pub target: String,
    pub global_config: Option<serde_json::Value>,
    pub step_outputs: HashMap<String, serde_json::Value>, // 步骤ID -> 输出数据
    pub variables: HashMap<String, serde_json::Value>,    // 用户定义的变量
}

impl WorkflowContext {
    pub fn new(target: String) -> Self {
        Self {
            target,
            global_config: None,
            step_outputs: HashMap::new(),
            variables: HashMap::new(),
        }
    }

    /// 获取步骤的输出数据
    pub fn get_step_output(&self, step_id: &str) -> Option<&serde_json::Value> {
        self.step_outputs.get(step_id)
    }

    /// 设置步骤的输出数据
    pub fn set_step_output(&mut self, step_id: String, output: serde_json::Value) {
        self.step_outputs.insert(step_id, output);
    }

    /// 获取变量值
    pub fn get_variable(&self, key: &str) -> Option<&serde_json::Value> {
        self.variables.get(key)
    }

    /// 设置变量值
    pub fn set_variable(&mut self, key: String, value: serde_json::Value) {
        self.variables.insert(key, value);
    }
}

/// 预定义的工作流模板
pub struct WorkflowTemplates;

impl WorkflowTemplates {
    /// 创建基础端口扫描工作流
    pub fn basic_port_scan() -> WorkflowDefinition {
        let now = chrono::Utc::now().naive_utc();

        WorkflowDefinition {
            id: Uuid::new_v4().to_string(),
            name: "基础端口扫描".to_string(),
            description: Some("使用 RustScan 进行基础端口扫描".to_string()),
            version: "1.0.0".to_string(),
            steps: vec![WorkflowStep {
                id: "port_scan".to_string(),
                name: "端口扫描".to_string(),
                description: Some("扫描目标的开放端口".to_string()),
                tool: "rustscan".to_string(),
                config: Some(serde_json::json!({
                    "timeout": 5000,
                    "threads": 1000
                })),
                depends_on: vec![],
                retry_count: 2,
                timeout: Some(300),
                continue_on_failure: false,
            }],
            global_config: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 创建综合扫描工作流
    pub fn comprehensive_scan() -> WorkflowDefinition {
        let now = chrono::Utc::now().naive_utc();

        WorkflowDefinition {
            id: Uuid::new_v4().to_string(),
            name: "综合安全扫描".to_string(),
            description: Some(
                "包含子域名发现、端口扫描、Web服务探测和漏洞扫描的完整工作流".to_string(),
            ),
            version: "1.0.0".to_string(),
            steps: vec![
                WorkflowStep {
                    id: "subdomain_discovery".to_string(),
                    name: "子域名发现".to_string(),
                    description: Some("发现目标域名的子域名".to_string()),
                    tool: "subfinder".to_string(),
                    config: Some(serde_json::json!({
                        "timeout": 30000,
                        "threads": 10
                    })),
                    depends_on: vec![],
                    retry_count: 1,
                    timeout: Some(600),
                    continue_on_failure: true,
                },
                WorkflowStep {
                    id: "dns_resolution".to_string(),
                    name: "DNS解析".to_string(),
                    description: Some("解析发现的子域名".to_string()),
                    tool: "dnsx".to_string(),
                    config: Some(serde_json::json!({
                        "timeout": 10000,
                        "threads": 25
                    })),
                    depends_on: vec!["subdomain_discovery".to_string()],
                    retry_count: 1,
                    timeout: Some(300),
                    continue_on_failure: true,
                },
                WorkflowStep {
                    id: "port_scan".to_string(),
                    name: "端口扫描".to_string(),
                    description: Some("扫描目标的开放端口".to_string()),
                    tool: "rustscan".to_string(),
                    config: Some(serde_json::json!({
                        "timeout": 5000,
                        "threads": 1000
                    })),
                    depends_on: vec![],
                    retry_count: 2,
                    timeout: Some(300),
                    continue_on_failure: false,
                },
                WorkflowStep {
                    id: "web_probe".to_string(),
                    name: "Web服务探测".to_string(),
                    description: Some("探测Web服务并获取基本信息".to_string()),
                    tool: "httpx".to_string(),
                    config: Some(serde_json::json!({
                        "timeout": 10000,
                        "threads": 50
                    })),
                    depends_on: vec!["port_scan".to_string(), "dns_resolution".to_string()],
                    retry_count: 1,
                    timeout: Some(600),
                    continue_on_failure: true,
                },
                WorkflowStep {
                    id: "vulnerability_scan".to_string(),
                    name: "漏洞扫描".to_string(),
                    description: Some("使用Nuclei进行漏洞扫描".to_string()),
                    tool: "nuclei".to_string(),
                    config: Some(serde_json::json!({
                        "timeout": 60000,
                        "threads": 25
                    })),
                    depends_on: vec!["web_probe".to_string()],
                    retry_count: 1,
                    timeout: Some(1800),
                    continue_on_failure: true,
                },
            ],
            global_config: Some(serde_json::json!({
                "max_parallel_steps": 3,
                "global_timeout": 7200
            })),
            created_at: now,
            updated_at: now,
        }
    }
}
