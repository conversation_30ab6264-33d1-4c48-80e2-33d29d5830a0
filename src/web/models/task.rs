//! 任务数据模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// 扫描任务状态
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum TaskStatus {
    #[sqlx(rename = "pending")]
    Pending,
    #[sqlx(rename = "running")]
    Running,
    #[sqlx(rename = "completed")]
    Completed,
    #[sqlx(rename = "failed")]
    Failed,
    #[sqlx(rename = "cancelled")]
    Cancelled,
}

/// 扫描类型
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ScanType {
    #[sqlx(rename = "quick")]
    Quick,
    #[sqlx(rename = "standard")]
    Standard,
    #[sqlx(rename = "deep")]
    Deep,
    #[sqlx(rename = "web")]
    Web,
    #[sqlx(rename = "custom")]
    Custom,
}

/// 扫描任务实体
#[derive(Debug, C<PERSON>, Serialize, Deserialize, FromRow)]
pub struct Task {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub target: String,
    pub scan_type: ScanType,
    pub status: TaskStatus,
    pub config: Option<String>, // JSON 配置
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 创建任务请求
#[derive(Debug, Deserialize)]
pub struct CreateTaskRequest {
    pub name: String,
    pub description: Option<String>,
    pub target: String,
    pub scan_type: ScanType,
    pub config: Option<serde_json::Value>,
}

/// 任务响应
#[derive(Debug, Serialize)]
pub struct TaskResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub target: String,
    pub scan_type: ScanType,
    pub status: TaskStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
}

impl From<Task> for TaskResponse {
    fn from(task: Task) -> Self {
        Self {
            id: task.id,
            name: task.name,
            description: task.description,
            target: task.target,
            scan_type: task.scan_type,
            status: task.status,
            created_at: task.created_at,
            updated_at: task.updated_at,
            started_at: task.started_at,
            completed_at: task.completed_at,
        }
    }
}
