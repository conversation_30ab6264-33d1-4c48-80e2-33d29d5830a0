//! 任务数据模型

use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::fmt;

/// 扫描任务状态
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum TaskStatus {
    #[sqlx(rename = "pending")]
    Pending,
    #[sqlx(rename = "running")]
    Running,
    #[sqlx(rename = "completed")]
    Completed,
    #[sqlx(rename = "failed")]
    Failed,
    #[sqlx(rename = "cancelled")]
    Cancelled,
}

/// 扫描类型
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ScanType {
    #[sqlx(rename = "port_scan")]
    PortScan,
    #[sqlx(rename = "domain_scan")]
    DomainScan,
    #[sqlx(rename = "web_scan")]
    WebScan,
    #[sqlx(rename = "vulnerability_scan")]
    VulnerabilityScan,
    #[sqlx(rename = "comprehensive_scan")]
    ComprehensiveScan,
}

impl fmt::Display for ScanType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let s = match self {
            ScanType::PortScan => "port_scan",
            ScanType::DomainScan => "domain_scan",
            ScanType::WebScan => "web_scan",
            ScanType::VulnerabilityScan => "vulnerability_scan",
            ScanType::ComprehensiveScan => "comprehensive_scan",
        };
        write!(f, "{}", s)
    }
}

impl From<String> for ScanType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "port_scan" => ScanType::PortScan,
            "domain_scan" => ScanType::DomainScan,
            "web_scan" => ScanType::WebScan,
            "vulnerability_scan" => ScanType::VulnerabilityScan,
            "comprehensive_scan" => ScanType::ComprehensiveScan,
            _ => ScanType::PortScan, // 默认值
        }
    }
}

/// 扫描任务实体
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Task {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub target: String,
    pub scan_type: ScanType,
    pub status: String,         // 使用 String 而不是枚举，简化数据库操作
    pub config: Option<String>, // JSON 配置
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub started_at: Option<NaiveDateTime>,
    pub completed_at: Option<NaiveDateTime>,
}

/// 创建任务请求
#[derive(Debug, Deserialize)]
pub struct CreateTaskRequest {
    pub name: String,
    pub description: Option<String>,
    pub target: String,
    pub scan_type: ScanType,
    pub config: Option<serde_json::Value>,
}

/// 任务响应
#[derive(Debug, Serialize)]
pub struct TaskResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub target: String,
    pub scan_type: ScanType,
    pub status: String,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub started_at: Option<NaiveDateTime>,
    pub completed_at: Option<NaiveDateTime>,
}

impl From<Task> for TaskResponse {
    fn from(task: Task) -> Self {
        Self {
            id: task.id,
            name: task.name,
            description: task.description,
            target: task.target,
            scan_type: task.scan_type,
            status: task.status,
            created_at: task.created_at,
            updated_at: task.updated_at,
            started_at: task.started_at,
            completed_at: task.completed_at,
        }
    }
}
