//! 任务调度器服务

use crate::web::services::executor::TaskExecutor;
use sqlx::{Pool, Sqlite};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{error, info, warn};

/// 任务调度器
pub struct TaskScheduler {
    pool: Pool<Sqlite>,
    executor: Arc<TaskExecutor>,
    running_tasks: Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
    is_running: Arc<RwLock<bool>>,
}

impl TaskScheduler {
    /// 创建新的任务调度器
    pub fn new(pool: Pool<Sqlite>, executor: Arc<TaskExecutor>) -> Self {
        Self {
            pool,
            executor,
            running_tasks: Arc::new(RwLock::new(HashMap::new())),
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// 启动调度器
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            warn!("任务调度器已经在运行中");
            return Ok(());
        }

        *is_running = true;
        drop(is_running);

        info!("🚀 任务调度器启动");

        // 启动定时任务检查
        let pool = self.pool.clone();
        let executor = self.executor.clone();
        let running_tasks = self.running_tasks.clone();
        let is_running_flag = self.is_running.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(5)); // 每5秒检查一次

            loop {
                interval.tick().await;

                // 检查是否应该停止
                {
                    let is_running = is_running_flag.read().await;
                    if !*is_running {
                        info!("任务调度器停止");
                        break;
                    }
                }

                // 检查待执行的任务
                if let Err(e) = Self::check_pending_tasks(&pool, &executor, &running_tasks).await {
                    error!("检查待执行任务时出错: {}", e);
                }

                // 清理已完成的任务句柄
                Self::cleanup_completed_tasks(&running_tasks).await;
            }
        });

        Ok(())
    }

    /// 停止调度器
    pub async fn stop(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut is_running = self.is_running.write().await;
        if !*is_running {
            warn!("任务调度器未在运行");
            return Ok(());
        }

        *is_running = false;
        drop(is_running);

        // 等待所有运行中的任务完成
        let mut running_tasks = self.running_tasks.write().await;
        for (task_id, handle) in running_tasks.drain() {
            info!("等待任务 {} 完成", task_id);
            handle.abort(); // 强制终止任务
        }

        info!("任务调度器已停止");
        Ok(())
    }

    /// 手动触发任务执行
    pub async fn execute_task_now(
        &self,
        task_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 检查任务是否已在运行
        {
            let running_tasks = self.running_tasks.read().await;
            if running_tasks.contains_key(task_id) {
                return Err("任务已在运行中".into());
            }
        }

        // 检查任务状态
        let task_status = self.get_task_status(task_id).await?;
        if task_status != "pending" {
            return Err(format!("任务状态不是 pending，当前状态: {}", task_status).into());
        }

        // 启动任务执行
        self.spawn_task_execution(task_id).await;

        Ok(())
    }

    /// 检查待执行的任务
    async fn check_pending_tasks(
        pool: &Pool<Sqlite>,
        executor: &Arc<TaskExecutor>,
        running_tasks: &Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 查询待执行的任务
        let pending_tasks = sqlx::query!(
            "SELECT id FROM tasks WHERE status = 'pending' ORDER BY created_at ASC LIMIT 10"
        )
        .fetch_all(pool)
        .await?;

        for task in pending_tasks {
            let task_id = task.id.unwrap_or_default();

            // 检查任务是否已在运行
            {
                let running_tasks_guard = running_tasks.read().await;
                if running_tasks_guard.contains_key(&task_id) {
                    continue;
                }
            }

            info!("发现待执行任务: {}", task_id);

            // 启动任务执行
            let executor_clone = executor.clone();
            let task_id_clone = task_id.clone();

            let handle = tokio::spawn(async move {
                if let Err(e) = executor_clone.execute_task(&task_id_clone).await {
                    error!("任务 {} 执行失败: {}", task_id_clone, e);
                }
            });

            // 记录运行中的任务
            {
                let mut running_tasks_guard = running_tasks.write().await;
                running_tasks_guard.insert(task_id, handle);
            }
        }

        Ok(())
    }

    /// 清理已完成的任务句柄
    async fn cleanup_completed_tasks(
        running_tasks: &Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
    ) {
        let mut to_remove = Vec::new();

        {
            let running_tasks_guard = running_tasks.read().await;
            for (task_id, handle) in running_tasks_guard.iter() {
                if handle.is_finished() {
                    to_remove.push(task_id.clone());
                }
            }
        }

        if !to_remove.is_empty() {
            let mut running_tasks_guard = running_tasks.write().await;
            for task_id in to_remove {
                running_tasks_guard.remove(&task_id);
                info!("清理已完成任务: {}", task_id);
            }
        }
    }

    /// 启动任务执行
    async fn spawn_task_execution(&self, task_id: &str) {
        let executor = self.executor.clone();
        let task_id_clone = task_id.to_string();
        let running_tasks = self.running_tasks.clone();

        let handle = tokio::spawn(async move {
            if let Err(e) = executor.execute_task(&task_id_clone).await {
                error!("任务 {} 执行失败: {}", task_id_clone, e);
            }
        });

        // 记录运行中的任务
        {
            let mut running_tasks_guard = running_tasks.write().await;
            running_tasks_guard.insert(task_id.to_string(), handle);
        }

        info!("任务 {} 已启动执行", task_id);
    }

    /// 获取任务状态
    async fn get_task_status(
        &self,
        task_id: &str,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let result = sqlx::query!("SELECT status FROM tasks WHERE id = ?", task_id)
            .fetch_one(&self.pool)
            .await?;

        Ok(result.status)
    }

    /// 获取运行中的任务列表
    pub async fn get_running_tasks(&self) -> Vec<String> {
        let running_tasks = self.running_tasks.read().await;
        running_tasks.keys().cloned().collect()
    }

    /// 取消任务执行
    pub async fn cancel_task(
        &self,
        task_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut running_tasks = self.running_tasks.write().await;

        if let Some(handle) = running_tasks.remove(task_id) {
            handle.abort();
            info!("任务 {} 已被取消", task_id);

            // 更新数据库中的任务状态
            let now = chrono::Utc::now().naive_utc();
            sqlx::query!(
                "UPDATE tasks SET status = 'cancelled', updated_at = ? WHERE id = ?",
                now,
                task_id
            )
            .execute(&self.pool)
            .await?;

            Ok(())
        } else {
            Err("任务未在运行中".into())
        }
    }

    /// 获取调度器状态
    pub async fn get_status(&self) -> SchedulerStatus {
        let is_running = *self.is_running.read().await;
        let running_tasks = self.running_tasks.read().await;
        let running_task_count = running_tasks.len();

        SchedulerStatus {
            is_running,
            running_task_count,
            running_tasks: running_tasks.keys().cloned().collect(),
        }
    }
}

/// 调度器状态
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SchedulerStatus {
    pub is_running: bool,
    pub running_task_count: usize,
    pub running_tasks: Vec<String>,
}
