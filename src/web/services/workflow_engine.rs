//! 工作流执行引擎

use crate::web::models::workflow::{
    StepStatus, WorkflowContext, WorkflowDefinition, WorkflowInstance, WorkflowStatus,
    WorkflowStep, WorkflowStepInstance,
};
use crate::web::tools::manager::ToolManager;
use crate::web::tools::{ToolConfig, ToolResult};
use sqlx::{Pool, Sqlite};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{timeout, Duration};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// 工作流执行引擎
pub struct WorkflowEngine {
    pool: Pool<Sqlite>,
    tool_manager: Arc<ToolManager>,
    running_workflows: Arc<RwLock<HashMap<String, WorkflowExecution>>>,
}

/// 工作流执行状态
#[derive(Debug)]
struct WorkflowExecution {
    instance: WorkflowInstance,
    definition: WorkflowDefinition,
    context: WorkflowContext,
    step_instances: HashMap<String, WorkflowStepInstance>,
    completed_steps: HashSet<String>,
    failed_steps: HashSet<String>,
}

impl WorkflowEngine {
    /// 创建新的工作流引擎
    pub fn new(pool: Pool<Sqlite>, tool_manager: Arc<ToolManager>) -> Self {
        Self {
            pool,
            tool_manager,
            running_workflows: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 启动工作流实例
    pub async fn start_workflow(
        &self,
        instance_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("启动工作流实例: {}", instance_id);

        // 获取工作流实例和定义
        let (instance, definition) = self.load_workflow_data(instance_id).await?;

        // 创建执行上下文
        let context = if let Some(context_str) = &instance.context {
            serde_json::from_str(context_str)?
        } else {
            WorkflowContext::new(instance.target.clone())
        };

        // 创建步骤实例
        let step_instances = self.create_step_instances(&instance, &definition).await?;

        // 创建工作流执行状态
        let execution = WorkflowExecution {
            instance: instance.clone(),
            definition,
            context,
            step_instances,
            completed_steps: HashSet::new(),
            failed_steps: HashSet::new(),
        };

        // 记录运行中的工作流
        {
            let mut running = self.running_workflows.write().await;
            running.insert(instance_id.to_string(), execution);
        }

        // 更新工作流状态为运行中
        self.update_workflow_status(instance_id, WorkflowStatus::Running)
            .await?;

        // 开始执行工作流
        let engine = self.clone();
        let instance_id_clone = instance_id.to_string();

        tokio::spawn(async move {
            if let Err(e) = engine.execute_workflow(&instance_id_clone).await {
                error!("工作流 {} 执行失败: {}", instance_id_clone, e);
                let _ = engine
                    .update_workflow_status(&instance_id_clone, WorkflowStatus::Failed)
                    .await;
            }
        });

        Ok(())
    }

    /// 执行工作流
    async fn execute_workflow(
        &self,
        instance_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("开始执行工作流: {}", instance_id);

        loop {
            // 获取可执行的步骤
            let ready_steps = self.get_ready_steps(instance_id).await?;

            if ready_steps.is_empty() {
                // 检查是否所有步骤都已完成
                if self.is_workflow_completed(instance_id).await? {
                    info!("工作流 {} 执行完成", instance_id);
                    self.update_workflow_status(instance_id, WorkflowStatus::Completed)
                        .await?;
                    break;
                } else if self.has_failed_critical_steps(instance_id).await? {
                    warn!("工作流 {} 因关键步骤失败而终止", instance_id);
                    self.update_workflow_status(instance_id, WorkflowStatus::Failed)
                        .await?;
                    break;
                } else {
                    // 等待一段时间后重新检查
                    tokio::time::sleep(Duration::from_secs(1)).await;
                    continue;
                }
            }

            // 并行执行准备好的步骤
            let mut handles = Vec::new();
            for step_id in ready_steps {
                let engine = self.clone();
                let instance_id_clone = instance_id.to_string();
                let step_id_clone = step_id.clone();

                let handle = tokio::spawn(async move {
                    engine
                        .execute_step(&instance_id_clone, &step_id_clone)
                        .await
                });

                handles.push((step_id, handle));
            }

            // 等待所有步骤完成
            for (step_id, handle) in handles {
                match handle.await {
                    Ok(Ok(_)) => {
                        debug!("步骤 {} 执行成功", step_id);
                    }
                    Ok(Err(e)) => {
                        error!("步骤 {} 执行失败: {}", step_id, e);
                    }
                    Err(e) => {
                        error!("步骤 {} 任务失败: {}", step_id, e);
                    }
                }
            }
        }

        // 清理运行中的工作流记录
        {
            let mut running = self.running_workflows.write().await;
            running.remove(instance_id);
        }

        Ok(())
    }

    /// 执行单个步骤
    async fn execute_step(
        &self,
        instance_id: &str,
        step_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("执行步骤: {} (工作流: {})", step_id, instance_id);

        // 获取步骤定义和实例
        let (step_def, mut step_instance) = self.get_step_data(instance_id, step_id).await?;

        // 更新步骤状态为运行中
        self.update_step_status(instance_id, step_id, StepStatus::Running)
            .await?;

        // 准备输入数据
        let input_data = self.prepare_step_input(instance_id, &step_def).await?;

        // 执行步骤（带重试）
        let mut retry_count = 0;
        let max_retries = step_def.retry_count;
        let mut last_error = None;

        while retry_count <= max_retries {
            match self.execute_step_with_tool(&step_def, &input_data).await {
                Ok(result) => {
                    // 执行成功
                    info!("步骤 {} 执行成功", step_id);

                    // 保存输出数据
                    self.save_step_output(instance_id, step_id, &result).await?;

                    // 更新步骤状态为完成
                    self.update_step_status(instance_id, step_id, StepStatus::Completed)
                        .await?;

                    // 标记步骤为已完成
                    self.mark_step_completed(instance_id, step_id).await?;

                    return Ok(());
                }
                Err(e) => {
                    warn!(
                        "步骤 {} 执行失败 (尝试 {}/{}): {}",
                        step_id,
                        retry_count + 1,
                        max_retries + 1,
                        e
                    );
                    last_error = Some(e);
                    retry_count += 1;

                    if retry_count <= max_retries {
                        // 等待后重试
                        tokio::time::sleep(Duration::from_secs(2_u64.pow(retry_count))).await;
                    }
                }
            }
        }

        // 所有重试都失败了
        let error_msg = format!(
            "步骤执行失败，已重试 {} 次: {}",
            max_retries,
            last_error.map(|e| e.to_string()).unwrap_or_default()
        );

        error!("{}", error_msg);

        // 更新步骤状态为失败
        self.update_step_status(instance_id, step_id, StepStatus::Failed)
            .await?;
        self.update_step_error(instance_id, step_id, &error_msg)
            .await?;

        // 标记步骤为失败
        self.mark_step_failed(instance_id, step_id).await?;

        Err(error_msg.into())
    }

    /// 使用工具执行步骤
    async fn execute_step_with_tool(
        &self,
        step_def: &WorkflowStep,
        input_data: &serde_json::Value,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        // 准备工具配置
        let mut tool_config = ToolConfig::default();

        if let Some(config) = &step_def.config {
            if let Ok(parsed_config) = serde_json::from_value::<ToolConfig>(config.clone()) {
                tool_config = parsed_config;
            }
        }

        // 从输入数据中提取目标
        let target = input_data
            .get("target")
            .and_then(|v| v.as_str())
            .unwrap_or_default();

        // 设置超时
        if let Some(timeout_secs) = step_def.timeout {
            tool_config.timeout = Some(timeout_secs * 1000); // 转换为毫秒
        }

        // 执行工具扫描
        let result = if let Some(timeout_duration) = step_def.timeout {
            timeout(
                Duration::from_secs(timeout_duration),
                self.tool_manager
                    .scan(&step_def.tool, target, Some(tool_config)),
            )
            .await??
        } else {
            self.tool_manager
                .scan(&step_def.tool, target, Some(tool_config))
                .await?
        };

        Ok(result)
    }

    /// 获取准备好执行的步骤
    async fn get_ready_steps(
        &self,
        instance_id: &str,
    ) -> Result<Vec<String>, Box<dyn std::error::Error + Send + Sync>> {
        let running = self.running_workflows.read().await;
        let execution = running.get(instance_id).ok_or("工作流实例不存在")?;

        let mut ready_steps = Vec::new();

        for step in &execution.definition.steps {
            let step_id = &step.id;

            // 跳过已完成或失败的步骤
            if execution.completed_steps.contains(step_id)
                || execution.failed_steps.contains(step_id)
            {
                continue;
            }

            // 检查步骤是否正在运行
            if let Some(step_instance) = execution.step_instances.get(step_id) {
                if step_instance.status == "running" {
                    continue;
                }
            }

            // 检查依赖是否满足
            let dependencies_met = step.depends_on.iter().all(|dep_id| {
                execution.completed_steps.contains(dep_id)
                    || (execution.failed_steps.contains(dep_id)
                        && execution
                            .definition
                            .steps
                            .iter()
                            .find(|s| &s.id == dep_id)
                            .map(|s| s.continue_on_failure)
                            .unwrap_or(false))
            });

            if dependencies_met {
                ready_steps.push(step_id.clone());
            }
        }

        Ok(ready_steps)
    }

    /// 检查工作流是否完成
    async fn is_workflow_completed(
        &self,
        instance_id: &str,
    ) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let running = self.running_workflows.read().await;
        let execution = running.get(instance_id).ok_or("工作流实例不存在")?;

        let total_steps = execution.definition.steps.len();
        let completed_or_failed = execution.completed_steps.len() + execution.failed_steps.len();

        Ok(completed_or_failed == total_steps)
    }

    /// 检查是否有关键步骤失败
    async fn has_failed_critical_steps(
        &self,
        instance_id: &str,
    ) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let running = self.running_workflows.read().await;
        let execution = running.get(instance_id).ok_or("工作流实例不存在")?;

        // 检查是否有不允许失败的步骤失败了
        for failed_step_id in &execution.failed_steps {
            if let Some(step_def) = execution
                .definition
                .steps
                .iter()
                .find(|s| &s.id == failed_step_id)
            {
                if !step_def.continue_on_failure {
                    return Ok(true);
                }
            }
        }

        Ok(false)
    }

    /// 准备步骤输入数据
    async fn prepare_step_input(
        &self,
        instance_id: &str,
        step_def: &WorkflowStep,
    ) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let running = self.running_workflows.read().await;
        let execution = running.get(instance_id).ok_or("工作流实例不存在")?;

        let mut input = serde_json::json!({
            "target": execution.context.target,
            "step_id": step_def.id,
            "step_name": step_def.name
        });

        // 添加依赖步骤的输出数据
        for dep_id in &step_def.depends_on {
            if let Some(dep_output) = execution.context.get_step_output(dep_id) {
                input[format!("dep_{}", dep_id)] = dep_output.clone();
            }
        }

        // 添加全局配置
        if let Some(global_config) = &execution.context.global_config {
            input["global_config"] = global_config.clone();
        }

        Ok(input)
    }

    /// 保存步骤输出数据
    async fn save_step_output(
        &self,
        instance_id: &str,
        step_id: &str,
        result: &ToolResult,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 更新内存中的上下文
        {
            let mut running = self.running_workflows.write().await;
            if let Some(execution) = running.get_mut(instance_id) {
                if let Some(data) = &result.data {
                    execution
                        .context
                        .set_step_output(step_id.to_string(), data.clone());
                }
            }
        }

        // 更新数据库中的步骤实例
        let output_json = serde_json::to_string(&result.data)?;
        let now = chrono::Utc::now().naive_utc();

        sqlx::query!(
            "UPDATE workflow_step_instances SET output_data = ?, completed_at = ?, updated_at = ? WHERE workflow_instance_id = ? AND step_id = ?",
            output_json,
            now,
            now,
            instance_id,
            step_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 标记步骤为已完成
    async fn mark_step_completed(
        &self,
        instance_id: &str,
        step_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut running = self.running_workflows.write().await;
        if let Some(execution) = running.get_mut(instance_id) {
            execution.completed_steps.insert(step_id.to_string());
        }
        Ok(())
    }

    /// 标记步骤为失败
    async fn mark_step_failed(
        &self,
        instance_id: &str,
        step_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut running = self.running_workflows.write().await;
        if let Some(execution) = running.get_mut(instance_id) {
            execution.failed_steps.insert(step_id.to_string());
        }
        Ok(())
    }

    /// 加载工作流数据
    async fn load_workflow_data(
        &self,
        instance_id: &str,
    ) -> Result<(WorkflowInstance, WorkflowDefinition), Box<dyn std::error::Error + Send + Sync>>
    {
        // 获取工作流实例
        let instance = sqlx::query_as!(
            WorkflowInstance,
            "SELECT * FROM workflow_instances WHERE id = ?",
            instance_id
        )
        .fetch_one(&self.pool)
        .await?;

        // 获取工作流定义
        let definition_row = sqlx::query!(
            "SELECT * FROM workflow_definitions WHERE id = ?",
            instance.workflow_id
        )
        .fetch_one(&self.pool)
        .await?;

        let definition: WorkflowDefinition = serde_json::from_str(&definition_row.definition_json)?;

        Ok((instance, definition))
    }

    /// 创建步骤实例
    async fn create_step_instances(
        &self,
        instance: &WorkflowInstance,
        definition: &WorkflowDefinition,
    ) -> Result<HashMap<String, WorkflowStepInstance>, Box<dyn std::error::Error + Send + Sync>>
    {
        let mut step_instances = HashMap::new();
        let now = chrono::Utc::now().naive_utc();

        for step in &definition.steps {
            let step_instance_id = Uuid::new_v4().to_string();

            let step_instance = WorkflowStepInstance {
                id: step_instance_id.clone(),
                workflow_instance_id: instance.id.clone(),
                step_id: step.id.clone(),
                name: step.name.clone(),
                tool: step.tool.clone(),
                status: "pending".to_string(),
                input_data: None,
                output_data: None,
                error_message: None,
                retry_count: 0,
                created_at: now,
                updated_at: now,
                started_at: None,
                completed_at: None,
            };

            // 插入到数据库
            sqlx::query!(
                r#"
                INSERT INTO workflow_step_instances
                (id, workflow_instance_id, step_id, name, tool, status, retry_count, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                "#,
                step_instance.id,
                step_instance.workflow_instance_id,
                step_instance.step_id,
                step_instance.name,
                step_instance.tool,
                step_instance.status,
                step_instance.retry_count,
                step_instance.created_at,
                step_instance.updated_at
            )
            .execute(&self.pool)
            .await?;

            step_instances.insert(step.id.clone(), step_instance);
        }

        Ok(step_instances)
    }

    /// 获取步骤数据
    async fn get_step_data(
        &self,
        instance_id: &str,
        step_id: &str,
    ) -> Result<(WorkflowStep, WorkflowStepInstance), Box<dyn std::error::Error + Send + Sync>>
    {
        let running = self.running_workflows.read().await;
        let execution = running.get(instance_id).ok_or("工作流实例不存在")?;

        let step_def = execution
            .definition
            .steps
            .iter()
            .find(|s| s.id == step_id)
            .ok_or("步骤定义不存在")?
            .clone();

        let step_instance = execution
            .step_instances
            .get(step_id)
            .ok_or("步骤实例不存在")?
            .clone();

        Ok((step_def, step_instance))
    }

    /// 更新工作流状态
    async fn update_workflow_status(
        &self,
        instance_id: &str,
        status: WorkflowStatus,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let status_str = match status {
            WorkflowStatus::Pending => "pending",
            WorkflowStatus::Running => "running",
            WorkflowStatus::Completed => "completed",
            WorkflowStatus::Failed => "failed",
            WorkflowStatus::Cancelled => "cancelled",
        };

        let now = chrono::Utc::now().naive_utc();

        // 使用简单的条件 SQL 而不是动态构建
        match status {
            WorkflowStatus::Running => {
                sqlx::query!(
                    "UPDATE workflow_instances SET status = ?, updated_at = ?, started_at = ? WHERE id = ?",
                    status_str,
                    now,
                    now,
                    instance_id
                )
                .execute(&self.pool)
                .await?;
            }
            WorkflowStatus::Completed | WorkflowStatus::Failed => {
                sqlx::query!(
                    "UPDATE workflow_instances SET status = ?, updated_at = ?, completed_at = ? WHERE id = ?",
                    status_str,
                    now,
                    now,
                    instance_id
                )
                .execute(&self.pool)
                .await?;
            }
            _ => {
                sqlx::query!(
                    "UPDATE workflow_instances SET status = ?, updated_at = ? WHERE id = ?",
                    status_str,
                    now,
                    instance_id
                )
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(())
    }

    /// 更新步骤状态
    async fn update_step_status(
        &self,
        instance_id: &str,
        step_id: &str,
        status: StepStatus,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let status_str = match status {
            StepStatus::Pending => "pending",
            StepStatus::Running => "running",
            StepStatus::Completed => "completed",
            StepStatus::Failed => "failed",
            StepStatus::Skipped => "skipped",
        };

        let now = chrono::Utc::now().naive_utc();

        // 使用简单的条件 SQL 而不是动态构建
        match status {
            StepStatus::Running => {
                sqlx::query!(
                    "UPDATE workflow_step_instances SET status = ?, updated_at = ?, started_at = ? WHERE workflow_instance_id = ? AND step_id = ?",
                    status_str,
                    now,
                    now,
                    instance_id,
                    step_id
                )
                .execute(&self.pool)
                .await?;
            }
            StepStatus::Completed | StepStatus::Failed => {
                sqlx::query!(
                    "UPDATE workflow_step_instances SET status = ?, updated_at = ?, completed_at = ? WHERE workflow_instance_id = ? AND step_id = ?",
                    status_str,
                    now,
                    now,
                    instance_id,
                    step_id
                )
                .execute(&self.pool)
                .await?;
            }
            _ => {
                sqlx::query!(
                    "UPDATE workflow_step_instances SET status = ?, updated_at = ? WHERE workflow_instance_id = ? AND step_id = ?",
                    status_str,
                    now,
                    instance_id,
                    step_id
                )
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(())
    }

    /// 更新步骤错误信息
    async fn update_step_error(
        &self,
        instance_id: &str,
        step_id: &str,
        error_message: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now().naive_utc();

        sqlx::query!(
            "UPDATE workflow_step_instances SET error_message = ?, updated_at = ? WHERE workflow_instance_id = ? AND step_id = ?",
            error_message,
            now,
            instance_id,
            step_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

impl Clone for WorkflowEngine {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
            tool_manager: self.tool_manager.clone(),
            running_workflows: self.running_workflows.clone(),
        }
    }
}
