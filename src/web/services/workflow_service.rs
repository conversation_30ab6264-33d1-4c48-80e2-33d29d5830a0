//! 工作流管理服务

use crate::web::models::workflow::{
    CreateWorkflowInstanceRequest, WorkflowContext, WorkflowDefinition, WorkflowInstance,
    WorkflowStepInstance, WorkflowTemplates,
};
use crate::web::services::workflow_engine::WorkflowEngine;
use sqlx::{Pool, Sqlite};
use std::sync::Arc;
use tracing::{error, info};
use uuid::Uuid;

/// 工作流管理服务
pub struct WorkflowService {
    pool: Pool<Sqlite>,
    engine: Arc<WorkflowEngine>,
}

impl WorkflowService {
    /// 创建新的工作流服务
    pub fn new(pool: Pool<Sqlite>, engine: Arc<WorkflowEngine>) -> Self {
        Self { pool, engine }
    }

    /// 初始化预定义工作流模板
    pub async fn initialize_templates(
        &self,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("初始化工作流模板");

        // 创建基础端口扫描模板
        let basic_scan = WorkflowTemplates::basic_port_scan();
        self.save_workflow_definition(&basic_scan).await?;

        // 创建综合扫描模板
        let comprehensive_scan = WorkflowTemplates::comprehensive_scan();
        self.save_workflow_definition(&comprehensive_scan).await?;

        info!("工作流模板初始化完成");
        Ok(())
    }

    /// 保存工作流定义
    pub async fn save_workflow_definition(
        &self,
        definition: &WorkflowDefinition,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let definition_json = serde_json::to_string(definition)?;

        sqlx::query!(
            r#"
            INSERT OR REPLACE INTO workflow_definitions 
            (id, name, description, version, definition_json, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            "#,
            definition.id,
            definition.name,
            definition.description,
            definition.version,
            definition_json,
            definition.created_at,
            definition.updated_at
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 获取所有工作流定义
    pub async fn get_workflow_definitions(
        &self,
    ) -> Result<Vec<WorkflowDefinition>, Box<dyn std::error::Error + Send + Sync>> {
        let rows = sqlx::query!(
            "SELECT definition_json FROM workflow_definitions ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut definitions = Vec::new();
        for row in rows {
            let definition: WorkflowDefinition = serde_json::from_str(&row.definition_json)?;
            definitions.push(definition);
        }

        Ok(definitions)
    }

    /// 根据ID获取工作流定义
    pub async fn get_workflow_definition(
        &self,
        workflow_id: &str,
    ) -> Result<Option<WorkflowDefinition>, Box<dyn std::error::Error + Send + Sync>> {
        let row = sqlx::query!(
            "SELECT definition_json FROM workflow_definitions WHERE id = ?",
            workflow_id
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            let definition: WorkflowDefinition = serde_json::from_str(&row.definition_json)?;
            Ok(Some(definition))
        } else {
            Ok(None)
        }
    }

    /// 创建工作流实例
    pub async fn create_workflow_instance(
        &self,
        request: &CreateWorkflowInstanceRequest,
    ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        // 验证工作流定义是否存在
        let definition = self
            .get_workflow_definition(&request.workflow_id)
            .await?
            .ok_or("工作流定义不存在")?;

        let instance_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now().naive_utc();

        // 创建执行上下文
        let mut context = WorkflowContext::new(request.target.clone());
        if let Some(user_context) = &request.context {
            context.global_config = Some(user_context.clone());
        }
        if let Some(global_config) = &definition.global_config {
            context.global_config = Some(global_config.clone());
        }

        let context_json = serde_json::to_string(&context)?;

        // 插入工作流实例
        sqlx::query!(
            r#"
            INSERT INTO workflow_instances 
            (id, workflow_id, name, target, status, context, created_at, updated_at)
            VALUES (?, ?, ?, ?, 'pending', ?, ?, ?)
            "#,
            instance_id,
            request.workflow_id,
            request.name,
            request.target,
            context_json,
            now,
            now
        )
        .execute(&self.pool)
        .await?;

        info!(
            "创建工作流实例: {} (定义: {})",
            instance_id, request.workflow_id
        );

        Ok(instance_id)
    }

    /// 启动工作流实例
    pub async fn start_workflow_instance(
        &self,
        instance_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("启动工作流实例: {}", instance_id);
        self.engine.start_workflow(instance_id).await
    }

    /// 获取工作流实例
    pub async fn get_workflow_instance(
        &self,
        instance_id: &str,
    ) -> Result<Option<WorkflowInstance>, Box<dyn std::error::Error + Send + Sync>> {
        let instance = sqlx::query_as!(
            WorkflowInstance,
            "SELECT * FROM workflow_instances WHERE id = ?",
            instance_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(instance)
    }

    /// 获取工作流实例列表
    pub async fn get_workflow_instances(
        &self,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<WorkflowInstance>, Box<dyn std::error::Error + Send + Sync>> {
        let limit = limit.unwrap_or(20).min(100);
        let offset = offset.unwrap_or(0);

        let instances = sqlx::query_as!(
            WorkflowInstance,
            "SELECT * FROM workflow_instances ORDER BY created_at DESC LIMIT ? OFFSET ?",
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(instances)
    }

    /// 获取工作流步骤实例
    pub async fn get_workflow_step_instances(
        &self,
        instance_id: &str,
    ) -> Result<Vec<WorkflowStepInstance>, Box<dyn std::error::Error + Send + Sync>> {
        let step_instances = sqlx::query_as!(
            WorkflowStepInstance,
            "SELECT * FROM workflow_step_instances WHERE workflow_instance_id = ? ORDER BY created_at ASC",
            instance_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(step_instances)
    }

    /// 取消工作流实例
    pub async fn cancel_workflow_instance(
        &self,
        instance_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("取消工作流实例: {}", instance_id);

        let now = chrono::Utc::now().naive_utc();

        // 更新工作流状态为已取消
        sqlx::query!(
            "UPDATE workflow_instances SET status = 'cancelled', updated_at = ?, completed_at = ? WHERE id = ?",
            now,
            now,
            instance_id
        )
        .execute(&self.pool)
        .await?;

        // 更新所有未完成的步骤状态为已跳过
        sqlx::query!(
            "UPDATE workflow_step_instances SET status = 'skipped', updated_at = ? WHERE workflow_instance_id = ? AND status IN ('pending', 'running')",
            now,
            instance_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 删除工作流实例
    pub async fn delete_workflow_instance(
        &self,
        instance_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("删除工作流实例: {}", instance_id);

        // 先取消工作流（如果正在运行）
        let _ = self.cancel_workflow_instance(instance_id).await;

        // 删除步骤实例
        sqlx::query!(
            "DELETE FROM workflow_step_instances WHERE workflow_instance_id = ?",
            instance_id
        )
        .execute(&self.pool)
        .await?;

        // 删除工作流实例
        sqlx::query!("DELETE FROM workflow_instances WHERE id = ?", instance_id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    /// 获取工作流统计信息
    pub async fn get_workflow_statistics(
        &self,
    ) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let total_definitions = sqlx::query!("SELECT COUNT(*) as count FROM workflow_definitions")
            .fetch_one(&self.pool)
            .await?
            .count;

        let total_instances = sqlx::query!("SELECT COUNT(*) as count FROM workflow_instances")
            .fetch_one(&self.pool)
            .await?
            .count;

        let running_instances = sqlx::query!(
            "SELECT COUNT(*) as count FROM workflow_instances WHERE status = 'running'"
        )
        .fetch_one(&self.pool)
        .await?
        .count;

        let completed_instances = sqlx::query!(
            "SELECT COUNT(*) as count FROM workflow_instances WHERE status = 'completed'"
        )
        .fetch_one(&self.pool)
        .await?
        .count;

        let failed_instances = sqlx::query!(
            "SELECT COUNT(*) as count FROM workflow_instances WHERE status = 'failed'"
        )
        .fetch_one(&self.pool)
        .await?
        .count;

        Ok(serde_json::json!({
            "definitions": {
                "total": total_definitions
            },
            "instances": {
                "total": total_instances,
                "running": running_instances,
                "completed": completed_instances,
                "failed": failed_instances,
                "pending": total_instances - running_instances - completed_instances - failed_instances
            }
        }))
    }
}
