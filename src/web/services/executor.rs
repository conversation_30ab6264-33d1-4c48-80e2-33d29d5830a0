//! 任务执行器服务

use crate::web::models::task::{ScanType, Task};
use crate::web::tools::manager::ToolManager;
use crate::web::tools::{ToolConfig, ToolResult};
use sqlx::{Pool, Sqlite};
use std::sync::Arc;
use tokio::sync::Semaphore;
use tracing::{error, info, warn};
use uuid::Uuid;

/// 任务执行器
pub struct TaskExecutor {
    pool: Pool<Sqlite>,
    tool_manager: Arc<ToolManager>,
    semaphore: Arc<Semaphore>, // 控制并发任务数量
}

impl TaskExecutor {
    /// 创建新的任务执行器
    pub fn new(pool: Pool<Sqlite>, tool_manager: Arc<ToolManager>, max_concurrent: usize) -> Self {
        Self {
            pool,
            tool_manager,
            semaphore: Arc::new(Semaphore::new(max_concurrent)),
        }
    }

    /// 执行单个任务
    pub async fn execute_task(
        &self,
        task_id: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 获取信号量许可
        let _permit = self.semaphore.acquire().await?;

        info!("开始执行任务: {}", task_id);

        // 获取任务详情
        let task = self.get_task(task_id).await?;

        // 更新任务状态为运行中
        self.update_task_status(task_id, "running").await?;

        // 根据扫描类型选择工具并执行
        let result = match task.scan_type {
            ScanType::PortScan => self.execute_port_scan(&task).await,
            ScanType::DomainScan => self.execute_domain_scan(&task).await,
            ScanType::WebScan => self.execute_web_scan(&task).await,
            ScanType::VulnerabilityScan => self.execute_vulnerability_scan(&task).await,
            ScanType::ComprehensiveScan => self.execute_comprehensive_scan(&task).await,
        };

        match result {
            Ok(scan_result) => {
                info!("任务 {} 执行成功", task_id);
                self.save_scan_result(task_id, &scan_result).await?;
                self.update_task_status(task_id, "completed").await?;
            }
            Err(e) => {
                error!("任务 {} 执行失败: {}", task_id, e);
                self.update_task_status(task_id, "failed").await?;
                return Err(e);
            }
        }

        Ok(())
    }

    /// 执行端口扫描
    async fn execute_port_scan(
        &self,
        task: &Task,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let config = self.parse_task_config(&task.config)?;
        self.tool_manager
            .scan("rustscan", &task.target, Some(config))
            .await
    }

    /// 执行域名扫描
    async fn execute_domain_scan(
        &self,
        task: &Task,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let config = self.parse_task_config(&task.config)?;

        // 先用 Subfinder 进行子域名发现
        let subfinder_result = self
            .tool_manager
            .scan("subfinder", &task.target, Some(config.clone()))
            .await?;

        // 然后用 DNSx 进行 DNS 解析
        let dnsx_result = self
            .tool_manager
            .scan("dnsx", &task.target, Some(config))
            .await?;

        // 合并结果
        let mut combined_data = serde_json::Map::new();
        if let Some(subfinder_data) = subfinder_result.data {
            combined_data.insert("subdomains".to_string(), subfinder_data);
        }
        if let Some(dnsx_data) = dnsx_result.data {
            combined_data.insert("dns_records".to_string(), dnsx_data);
        }

        Ok(ToolResult {
            success: subfinder_result.success && dnsx_result.success,
            output: format!("{}\n{}", subfinder_result.output, dnsx_result.output),
            error: subfinder_result.error.or(dnsx_result.error),
            data: Some(serde_json::Value::Object(combined_data)),
            execution_time: subfinder_result.execution_time + dnsx_result.execution_time,
        })
    }

    /// 执行 Web 扫描
    async fn execute_web_scan(
        &self,
        task: &Task,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let config = self.parse_task_config(&task.config)?;
        self.tool_manager
            .scan("httpx", &task.target, Some(config))
            .await
    }

    /// 执行漏洞扫描
    async fn execute_vulnerability_scan(
        &self,
        task: &Task,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let config = self.parse_task_config(&task.config)?;
        self.tool_manager
            .scan("nuclei", &task.target, Some(config))
            .await
    }

    /// 执行综合扫描
    async fn execute_comprehensive_scan(
        &self,
        task: &Task,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let config = self.parse_task_config(&task.config)?;

        info!("开始综合扫描: {}", task.target);

        // 1. 端口扫描
        let port_result = self
            .tool_manager
            .scan("rustscan", &task.target, Some(config.clone()))
            .await?;

        // 2. 域名扫描（如果目标是域名）
        let domain_result =
            if task.target.contains('.') && !task.target.parse::<std::net::IpAddr>().is_ok() {
                Some(
                    self.tool_manager
                        .scan("subfinder", &task.target, Some(config.clone()))
                        .await?,
                )
            } else {
                None
            };

        // 3. Web 服务探测
        let web_result = self
            .tool_manager
            .scan("httpx", &task.target, Some(config.clone()))
            .await?;

        // 4. 漏洞扫描
        let vuln_result = self
            .tool_manager
            .scan("nuclei", &task.target, Some(config))
            .await?;

        // 计算总执行时间
        let domain_execution_time = domain_result
            .as_ref()
            .map(|r| r.execution_time)
            .unwrap_or(0);
        let total_time = port_result.execution_time
            + web_result.execution_time
            + vuln_result.execution_time
            + domain_execution_time;

        // 合并所有结果
        let mut combined_data = serde_json::Map::new();
        if let Some(port_data) = port_result.data {
            combined_data.insert("ports".to_string(), port_data);
        }
        if let Some(domain_result) = domain_result {
            if let Some(domain_data) = domain_result.data {
                combined_data.insert("subdomains".to_string(), domain_data);
            }
        }
        if let Some(web_data) = web_result.data {
            combined_data.insert("web_services".to_string(), web_data);
        }
        if let Some(vuln_data) = vuln_result.data {
            combined_data.insert("vulnerabilities".to_string(), vuln_data);
        }

        Ok(ToolResult {
            success: port_result.success && web_result.success && vuln_result.success,
            output: format!(
                "综合扫描完成\n端口扫描: {}\nWeb扫描: {}\n漏洞扫描: {}",
                port_result.output, web_result.output, vuln_result.output
            ),
            error: None,
            data: Some(serde_json::Value::Object(combined_data)),
            execution_time: total_time,
        })
    }

    /// 解析任务配置
    fn parse_task_config(
        &self,
        config: &Option<String>,
    ) -> Result<ToolConfig, Box<dyn std::error::Error + Send + Sync>> {
        match config {
            Some(config_str) => {
                let config: ToolConfig = serde_json::from_str(config_str)?;
                Ok(config)
            }
            None => Ok(ToolConfig::default()),
        }
    }

    /// 获取任务详情
    async fn get_task(
        &self,
        task_id: &str,
    ) -> Result<Task, Box<dyn std::error::Error + Send + Sync>> {
        let task = sqlx::query_as!(Task, "SELECT * FROM tasks WHERE id = ?", task_id)
            .fetch_one(&self.pool)
            .await?;

        Ok(task)
    }

    /// 更新任务状态
    async fn update_task_status(
        &self,
        task_id: &str,
        status: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let now = chrono::Utc::now().naive_utc();

        let mut query = sqlx::QueryBuilder::new("UPDATE tasks SET status = ?, updated_at = ?");
        query.push_bind(status);
        query.push_bind(now);

        if status == "running" {
            query.push(", started_at = ?");
            query.push_bind(now);
        } else if status == "completed" || status == "failed" {
            query.push(", completed_at = ?");
            query.push_bind(now);
        }

        query.push(" WHERE id = ?");
        query.push_bind(task_id);

        query.build().execute(&self.pool).await?;

        Ok(())
    }

    /// 保存扫描结果
    async fn save_scan_result(
        &self,
        task_id: &str,
        result: &ToolResult,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let result_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now().naive_utc();

        let result_data = serde_json::to_string(&result.data)?;

        // 创建一个临时的 target_id，实际应用中应该从任务中获取
        let target_id = format!("{}-target", task_id);

        sqlx::query!(
            r#"
            INSERT INTO scan_results (id, task_id, target_id, tool, result_type, data, created_at)
            VALUES (?, ?, ?, 'comprehensive', 'scan_result', ?, ?)
            "#,
            result_id,
            task_id,
            target_id,
            result_data,
            now
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
