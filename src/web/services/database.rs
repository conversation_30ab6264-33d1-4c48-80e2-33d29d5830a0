//! 数据库服务
//!
//! 提供数据库连接管理和基础操作

use anyhow::Result;
use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::sync::Arc;

/// 数据库服务
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct DatabaseService {
    pool: Arc<Pool<Sqlite>>,
}

impl DatabaseService {
    /// 创建新的数据库服务实例
    pub async fn new(database_url: &str) -> Result<Self> {
        // 确保数据库文件的目录存在
        if let Some(parent) =
            std::path::Path::new(database_url.trim_start_matches("sqlite:")).parent()
        {
            std::fs::create_dir_all(parent)?;
        }

        // 创建数据库连接，如果文件不存在会自动创建
        let pool = SqlitePool::connect(database_url).await?;

        Ok(Self {
            pool: Arc::new(pool),
        })
    }

    /// 获取数据库连接池
    pub fn pool(&self) -> &Pool<Sqlite> {
        &self.pool
    }

    /// 运行数据库迁移
    pub async fn migrate(&self) -> Result<()> {
        // 手动执行初始化 SQL，因为 sqlx::migrate! 需要编译时的 migrations 目录
        self.init_database().await?;
        Ok(())
    }

    /// 初始化数据库表结构
    async fn init_database(&self) -> Result<()> {
        // 执行基础表初始化
        let init_sql = include_str!("../../../migrations/001_initial.sql");
        self.execute_sql_file(init_sql).await?;

        // 执行工作流表初始化
        let workflow_sql = include_str!("../../../migrations/004_create_workflow_tables.sql");
        self.execute_sql_file(workflow_sql).await?;

        Ok(())
    }

    /// 执行 SQL 文件
    async fn execute_sql_file(&self, sql_content: &str) -> Result<()> {
        // 分割 SQL 语句并逐个执行
        for statement in sql_content.split(';') {
            let statement = statement.trim();
            if !statement.is_empty() {
                sqlx::query(statement).execute(&*self.pool).await?;
            }
        }
        Ok(())
    }

    /// 检查数据库连接
    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1").execute(&*self.pool).await?;
        Ok(())
    }
}
