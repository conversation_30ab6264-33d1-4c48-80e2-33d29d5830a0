//! 数据库服务
//! 
//! 提供数据库连接管理和基础操作

use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::sync::Arc;
use anyhow::Result;

/// 数据库服务
#[derive(Debu<PERSON>, Clone)]
pub struct DatabaseService {
    pool: Arc<Pool<Sqlite>>,
}

impl DatabaseService {
    /// 创建新的数据库服务实例
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect(database_url).await?;
        
        Ok(Self {
            pool: Arc::new(pool),
        })
    }
    
    /// 获取数据库连接池
    pub fn pool(&self) -> &Pool<Sqlite> {
        &self.pool
    }
    
    /// 运行数据库迁移
    pub async fn migrate(&self) -> Result<()> {
        sqlx::migrate!("./migrations").run(&*self.pool).await?;
        Ok(())
    }
    
    /// 检查数据库连接
    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1").execute(&*self.pool).await?;
        Ok(())
    }
}
