//! RustScan Web 安全扫描平台
//!
//! 基于 Actix-web 构建的现代化 Web 安全扫描平台
//! 集成多种安全扫描工具，提供统一的 Web 界面和 API

use actix_cors::Cors;
use actix_web::{middleware::Logger, web, App, HttpServer};
use tracing::{info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

mod common;
mod handlers;
mod models;
mod services;
mod tools;
mod utils;

use handlers::{config, health, results, tasks, tools as tool_handlers, workflow_simple, websocket};
use services::database::DatabaseService;
use std::sync::Arc;
use tools::manager::ToolManager;

/// Web 服务器配置
#[derive(Debug, Clone)]
pub struct AppConfig {
    pub host: String,
    pub port: u16,
    pub database_url: String,
    pub static_files_path: Option<String>,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 8080,
            database_url: "sqlite:data/rustscan.db".to_string(),
            static_files_path: Some("./frontend/dist".to_string()),
        }
    }
}

/// 配置应用路由和中间件
fn configure_app(cfg: &mut web::ServiceConfig) {
    cfg
        // 健康检查路由
        .route("/api/health", web::get().to(health::health_check))
        // WebSocket 路由
        .route("/ws", web::get().to(websocket::websocket_handler))
        // API 路由组
        .service(
            web::scope("/api/v1")
                // 任务管理路由
                .route("/tasks", web::get().to(tasks::get_tasks))
                .route("/tasks", web::post().to(tasks::create_task))
                .route("/tasks/{id}", web::get().to(tasks::get_task))
                .route("/tasks/{id}", web::delete().to(tasks::delete_task))
                // 扫描结果路由
                .route("/results", web::get().to(results::get_results))
                .route("/results/{id}", web::get().to(results::get_result))
                // 系统配置路由
                .route("/config", web::get().to(config::get_config))
                .route("/config", web::put().to(config::update_config))
                // 工具管理路由
                .route("/tools", web::get().to(tool_handlers::get_tools))
                .route("/tools/{name}", web::get().to(tool_handlers::get_tool_info))
                .route(
                    "/tools/{name}/check",
                    web::get().to(tool_handlers::check_tool_availability),
                )
                // 工作流管理路由
                // 工作流管理路由（演示模式）
                .route(
                    "/workflow-templates",
                    web::get().to(workflow_simple::get_workflow_templates),
                )
                .route(
                    "/workflow-instances",
                    web::get().to(workflow_simple::get_workflow_instances),
                )
                .route(
                    "/workflow-instances",
                    web::post().to(workflow_simple::create_workflow_instance_simple),
                )
                .route(
                    "/workflow-instances/{id}/status",
                    web::get().to(workflow_simple::get_workflow_status),
                )
                .route(
                    "/workflow-instances/{id}/stop",
                    web::post().to(workflow_simple::stop_workflow_instance),
                )
                .route(
                    "/workflow-instances/{id}/restart",
                    web::post().to(workflow_simple::restart_workflow_instance),
                )
                .route(
                    "/workflow-instances/{id}",
                    web::delete().to(workflow_simple::delete_workflow_instance),
                )
                .route(
                    "/workflow-statistics",
                    web::get().to(workflow_simple::get_workflow_statistics_simple),
                )
                .route(
                    "/workflow-demo/{type}",
                    web::get().to(workflow_simple::demo_workflow_execution),
                ),
        );
}

/// 初始化日志系统
fn init_logging() {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "rustscan_web=info,actix_web=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
}

/// 应用程序入口点
#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // 初始化日志
    init_logging();

    let config = AppConfig::default();

    info!("🚀 启动 RustScan Web 安全扫描平台");
    info!("📍 服务地址: http://{}:{}", config.host, config.port);
    info!("🗄️ 数据库: {}", config.database_url);

    // 初始化数据库
    let database_service = match DatabaseService::new(&config.database_url).await {
        Ok(service) => {
            info!("✅ 数据库连接成功");
            service
        }
        Err(e) => {
            warn!("❌ 数据库连接失败: {}", e);
            return Err(std::io::Error::new(std::io::ErrorKind::Other, e));
        }
    };

    // 运行数据库迁移
    if let Err(e) = database_service.migrate().await {
        warn!("⚠️ 数据库迁移失败: {}", e);
        return Err(std::io::Error::new(std::io::ErrorKind::Other, e));
    }

    info!("✅ 数据库迁移完成");

    // 初始化工具管理器
    let tool_manager = Arc::new(ToolManager::new());
    info!("🔧 工具管理器初始化完成");

    // 工作流协调器已实现（演示模式）
    info!("📋 工作流协调器初始化完成（演示模式）");

    // 启动 HTTP 服务器
    HttpServer::new(move || {
        let cors = Cors::default()
            .allow_any_origin()
            .allow_any_method()
            .allow_any_header()
            .max_age(3600);

        App::new()
            .app_data(web::Data::new(database_service.clone()))
            .app_data(web::Data::new(tool_manager.clone()))
            .wrap(cors)
            .wrap(Logger::default())
            .wrap(tracing_actix_web::TracingLogger::default())
            .configure(configure_app)
    })
    .bind(format!("{}:{}", config.host, config.port))?
    .run()
    .await
}
