//! 任务管理处理器

use crate::web::models::task::{CreateTaskRequest, ScanType, Task};
use crate::web::services::database::DatabaseService;
use crate::web::services::scheduler::TaskScheduler;
use actix_web::{web, HttpResponse, Result};
use serde_json::json;
use std::sync::Arc;
use uuid::Uuid;

/// 获取任务列表
pub async fn get_tasks(
    db: web::Data<DatabaseService>,
    query: web::Query<serde_json::Value>,
) -> Result<HttpResponse> {
    let page = query.get("page").and_then(|v| v.as_u64()).unwrap_or(1) as u32;
    let size = query.get("size").and_then(|v| v.as_u64()).unwrap_or(20) as u32;
    let size = size.min(100); // 限制最大页面大小

    let offset = (page - 1) * size;

    match sqlx::query_as!(
        Task,
        "SELECT * FROM tasks ORDER BY created_at DESC LIMIT ? OFFSET ?",
        size,
        offset
    )
    .fetch_all(db.pool())
    .await
    {
        Ok(tasks) => {
            // 获取总数
            let total = sqlx::query!("SELECT COUNT(*) as count FROM tasks")
                .fetch_one(db.pool())
                .await
                .map(|row| row.count)
                .unwrap_or(0);

            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "data": {
                    "tasks": tasks,
                    "total": total,
                    "page": page,
                    "size": size,
                    "pages": (total as f64 / size as f64).ceil() as u32
                }
            })))
        }
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("获取任务列表失败: {}", e)
            }
        }))),
    }
}

/// 创建新任务
pub async fn create_task(
    db: web::Data<DatabaseService>,
    scheduler: web::Data<Arc<TaskScheduler>>,
    req: web::Json<CreateTaskRequest>,
) -> Result<HttpResponse> {
    let task_id = Uuid::new_v4().to_string();
    let now = chrono::Utc::now().naive_utc();

    // 序列化配置
    let config_json = req
        .config
        .as_ref()
        .map(|c| serde_json::to_string(c).unwrap_or_default());
    let scan_type_str = req.scan_type.to_string();

    match sqlx::query!(
        r#"
        INSERT INTO tasks (id, name, description, target, scan_type, status, config, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'pending', ?, ?, ?)
        "#,
        task_id,
        req.name,
        req.description,
        req.target,
        scan_type_str,
        config_json,
        now,
        now
    )
    .execute(db.pool())
    .await
    {
        Ok(_) => {
            // 如果调度器正在运行，任务会自动被调度执行
            Ok(HttpResponse::Ok().json(json!({
                "success": true,
                "data": {
                    "task_id": task_id,
                    "message": "任务创建成功"
                }
            })))
        }
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("创建任务失败: {}", e)
            }
        })))
    }
}

/// 获取任务详情
pub async fn get_task(
    path: web::Path<String>,
    db: web::Data<DatabaseService>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();

    match sqlx::query_as!(Task, "SELECT * FROM tasks WHERE id = ?", task_id)
        .fetch_optional(db.pool())
        .await
    {
        Ok(Some(task)) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": task
        }))),
        Ok(None) => Ok(HttpResponse::NotFound().json(json!({
            "success": false,
            "error": {
                "code": "TASK_NOT_FOUND",
                "message": format!("任务 '{}' 不存在", task_id)
            }
        }))),
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("获取任务详情失败: {}", e)
            }
        }))),
    }
}

/// 删除任务
pub async fn delete_task(
    path: web::Path<String>,
    db: web::Data<DatabaseService>,
    scheduler: web::Data<Arc<TaskScheduler>>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();

    // 先尝试取消正在运行的任务
    let _ = scheduler.cancel_task(&task_id).await;

    match sqlx::query!("DELETE FROM tasks WHERE id = ?", task_id)
        .execute(db.pool())
        .await
    {
        Ok(result) => {
            if result.rows_affected() > 0 {
                Ok(HttpResponse::Ok().json(json!({
                    "success": true,
                    "data": {
                        "task_id": task_id,
                        "message": "任务删除成功"
                    }
                })))
            } else {
                Ok(HttpResponse::NotFound().json(json!({
                    "success": false,
                    "error": {
                        "code": "TASK_NOT_FOUND",
                        "message": format!("任务 '{}' 不存在", task_id)
                    }
                })))
            }
        }
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("删除任务失败: {}", e)
            }
        }))),
    }
}

/// 手动执行任务
pub async fn execute_task(
    path: web::Path<String>,
    scheduler: web::Data<Arc<TaskScheduler>>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();

    match scheduler.execute_task_now(&task_id).await {
        Ok(_) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "task_id": task_id,
                "message": "任务已开始执行"
            }
        }))),
        Err(e) => Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "error": {
                "code": "EXECUTION_ERROR",
                "message": format!("执行任务失败: {}", e)
            }
        }))),
    }
}

/// 取消任务执行
pub async fn cancel_task(
    path: web::Path<String>,
    scheduler: web::Data<Arc<TaskScheduler>>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();

    match scheduler.cancel_task(&task_id).await {
        Ok(_) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "task_id": task_id,
                "message": "任务已取消"
            }
        }))),
        Err(e) => Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "error": {
                "code": "CANCEL_ERROR",
                "message": format!("取消任务失败: {}", e)
            }
        }))),
    }
}
