//! WebSocket 处理器

use actix_web::{web, HttpRequest, HttpResponse, Result};
use actix_ws::{Message, MessageStream, Session};
use futures_util::StreamExt;
use serde_json::json;
use std::time::{Duration, Instant};
use tokio::time::interval;

/// WebSocket 连接处理
pub async fn websocket_handler(
    req: HttpRequest,
    stream: web::Payload,
) -> Result<HttpResponse> {
    let (response, session, mut msg_stream) = actix_ws::handle(&req, stream)?;

    // 启动 WebSocket 会话处理
    actix_web::rt::spawn(websocket_session(session, msg_stream));

    Ok(response)
}

/// WebSocket 会话处理
async fn websocket_session(mut session: Session, mut msg_stream: MessageStream) {
    let mut last_heartbeat = Instant::now();
    let mut interval = interval(Duration::from_secs(5));

    loop {
        tokio::select! {
            // 处理来自客户端的消息
            Some(msg) = msg_stream.next() => {
                match msg {
                    Ok(Message::Text(text)) => {
                        // 处理文本消息
                        if let Err(_) = handle_text_message(&mut session, &text).await {
                            break;
                        }
                        last_heartbeat = Instant::now();
                    }
                    Ok(Message::Ping(bytes)) => {
                        // 响应 ping
                        if session.pong(&bytes).await.is_err() {
                            break;
                        }
                        last_heartbeat = Instant::now();
                    }
                    Ok(Message::Pong(_)) => {
                        last_heartbeat = Instant::now();
                    }
                    Ok(Message::Close(_)) => {
                        break;
                    }
                    _ => {}
                }
            }

            // 定期发送心跳和状态更新
            _ = interval.tick() => {
                // 检查心跳超时
                if Instant::now().duration_since(last_heartbeat) > Duration::from_secs(60) {
                    break;
                }

                // 发送状态更新
                if let Err(_) = send_status_update(&mut session).await {
                    break;
                }
            }
        }
    }
}

/// 处理文本消息
async fn handle_text_message(session: &mut Session, text: &str) -> Result<(), actix_ws::Closed> {
    // 尝试解析 JSON 消息
    if let Ok(msg) = serde_json::from_str::<serde_json::Value>(text) {
        match msg.get("type").and_then(|t| t.as_str()) {
            Some("subscribe") => {
                // 客户端订阅更新
                let response = json!({
                    "type": "subscription_confirmed",
                    "message": "已订阅实时更新"
                });
                session.text(response.to_string()).await?;
            }
            Some("ping") => {
                // 响应客户端 ping
                let response = json!({
                    "type": "pong",
                    "timestamp": chrono::Utc::now().to_rfc3339()
                });
                session.text(response.to_string()).await?;
            }
            _ => {
                // 未知消息类型
                let response = json!({
                    "type": "error",
                    "message": "未知消息类型"
                });
                session.text(response.to_string()).await?;
            }
        }
    }

    Ok(())
}

/// 发送状态更新
async fn send_status_update(session: &mut Session) -> Result<(), actix_ws::Closed> {
    // 模拟发送工作流实例状态更新
    let update = json!({
        "type": "workflow_status_update",
        "data": {
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "active_instances": 2,
            "completed_today": 5
        }
    });

    session.text(update.to_string()).await
}
