//! 扫描结果处理器

use actix_web::{HttpResponse, Result};
use serde_json::json;

/// 获取扫描结果列表
pub async fn get_results() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(json!({
        "results": [],
        "total": 0,
        "message": "扫描结果列表功能开发中"
    })))
}

/// 获取扫描结果详情
pub async fn get_result() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(json!({
        "message": "获取扫描结果详情功能开发中"
    })))
}
