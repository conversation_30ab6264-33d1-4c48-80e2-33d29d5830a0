//! 扫描工具管理处理器

use super::super::tools::manager::ToolManager;
use actix_web::{web, HttpResponse, Result};
use serde_json::json;
use std::sync::Arc;

/// 获取所有工具信息
pub async fn get_tools(tool_manager: web::Data<Arc<ToolManager>>) -> Result<HttpResponse> {
    let tools_info = tool_manager.get_all_tools_info().await;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": {
            "tools": tools_info,
            "count": tools_info.len()
        }
    })))
}

/// 获取特定工具信息
pub async fn get_tool_info(
    path: web::Path<String>,
    tool_manager: web::Data<Arc<ToolManager>>,
) -> Result<HttpResponse> {
    let tool_name = path.into_inner();

    match tool_manager.get_tool_info(&tool_name).await {
        Some(info) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": info
        }))),
        None => Ok(HttpResponse::NotFound().json(json!({
            "success": false,
            "error": {
                "code": "TOOL_NOT_FOUND",
                "message": format!("工具 '{}' 不存在", tool_name)
            }
        }))),
    }
}

/// 检查工具可用性
pub async fn check_tool_availability(
    path: web::Path<String>,
    tool_manager: web::Data<Arc<ToolManager>>,
) -> Result<HttpResponse> {
    let tool_name = path.into_inner();

    let available = tool_manager.is_tool_available(&tool_name).await;

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "data": {
            "tool": tool_name,
            "available": available
        }
    })))
}
