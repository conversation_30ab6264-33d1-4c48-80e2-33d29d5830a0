//! 工作流管理处理器

use crate::web::models::workflow::CreateWorkflowInstanceRequest;
use crate::web::services::workflow_service::WorkflowService;
use actix_web::{web, HttpResponse, Result};
use serde_json::json;
use std::sync::Arc;

/// 获取工作流定义列表
pub async fn get_workflow_definitions(
    workflow_service: web::Data<Arc<WorkflowService>>,
) -> Result<HttpResponse> {
    match workflow_service.get_workflow_definitions().await {
        Ok(definitions) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "workflows": definitions,
                "count": definitions.len()
            }
        }))),
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("获取工作流定义失败: {}", e)
            }
        }))),
    }
}

/// 获取特定工作流定义
pub async fn get_workflow_definition(
    path: web::Path<String>,
    workflow_service: web::Data<Arc<WorkflowService>>,
) -> Result<HttpResponse> {
    let workflow_id = path.into_inner();

    match workflow_service.get_workflow_definition(&workflow_id).await {
        Ok(Some(definition)) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": definition
        }))),
        Ok(None) => Ok(HttpResponse::NotFound().json(json!({
            "success": false,
            "error": {
                "code": "WORKFLOW_NOT_FOUND",
                "message": format!("工作流定义 '{}' 不存在", workflow_id)
            }
        }))),
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("获取工作流定义失败: {}", e)
            }
        }))),
    }
}

/// 创建工作流实例
pub async fn create_workflow_instance(
    workflow_service: web::Data<Arc<WorkflowService>>,
    req: web::Json<CreateWorkflowInstanceRequest>,
) -> Result<HttpResponse> {
    match workflow_service.create_workflow_instance(&req).await {
        Ok(instance_id) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "instance_id": instance_id,
                "message": "工作流实例创建成功"
            }
        }))),
        Err(e) => Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "error": {
                "code": "CREATE_ERROR",
                "message": format!("创建工作流实例失败: {}", e)
            }
        }))),
    }
}

/// 启动工作流实例
pub async fn start_workflow_instance(
    path: web::Path<String>,
    workflow_service: web::Data<Arc<WorkflowService>>,
) -> Result<HttpResponse> {
    let instance_id = path.into_inner();

    match workflow_service.start_workflow_instance(&instance_id).await {
        Ok(_) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "instance_id": instance_id,
                "message": "工作流实例已启动"
            }
        }))),
        Err(e) => Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "error": {
                "code": "START_ERROR",
                "message": format!("启动工作流实例失败: {}", e)
            }
        }))),
    }
}

/// 获取工作流实例列表
pub async fn get_workflow_instances(
    workflow_service: web::Data<Arc<WorkflowService>>,
    query: web::Query<serde_json::Value>,
) -> Result<HttpResponse> {
    let page = query.get("page").and_then(|v| v.as_u64()).unwrap_or(1) as u32;
    let size = query.get("size").and_then(|v| v.as_u64()).unwrap_or(20) as u32;
    let size = size.min(100);

    let offset = (page - 1) * size;

    match workflow_service
        .get_workflow_instances(Some(size), Some(offset))
        .await
    {
        Ok(instances) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "instances": instances,
                "count": instances.len(),
                "page": page,
                "size": size
            }
        }))),
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("获取工作流实例失败: {}", e)
            }
        }))),
    }
}

/// 获取工作流实例详情
pub async fn get_workflow_instance(
    path: web::Path<String>,
    workflow_service: web::Data<Arc<WorkflowService>>,
) -> Result<HttpResponse> {
    let instance_id = path.into_inner();

    match workflow_service.get_workflow_instance(&instance_id).await {
        Ok(Some(instance)) => {
            // 获取步骤实例
            match workflow_service
                .get_workflow_step_instances(&instance_id)
                .await
            {
                Ok(step_instances) => Ok(HttpResponse::Ok().json(json!({
                    "success": true,
                    "data": {
                        "instance": instance,
                        "steps": step_instances
                    }
                }))),
                Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
                    "success": false,
                    "error": {
                        "code": "DATABASE_ERROR",
                        "message": format!("获取步骤实例失败: {}", e)
                    }
                }))),
            }
        }
        Ok(None) => Ok(HttpResponse::NotFound().json(json!({
            "success": false,
            "error": {
                "code": "INSTANCE_NOT_FOUND",
                "message": format!("工作流实例 '{}' 不存在", instance_id)
            }
        }))),
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("获取工作流实例失败: {}", e)
            }
        }))),
    }
}

/// 取消工作流实例
pub async fn cancel_workflow_instance(
    path: web::Path<String>,
    workflow_service: web::Data<Arc<WorkflowService>>,
) -> Result<HttpResponse> {
    let instance_id = path.into_inner();

    match workflow_service
        .cancel_workflow_instance(&instance_id)
        .await
    {
        Ok(_) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "instance_id": instance_id,
                "message": "工作流实例已取消"
            }
        }))),
        Err(e) => Ok(HttpResponse::BadRequest().json(json!({
            "success": false,
            "error": {
                "code": "CANCEL_ERROR",
                "message": format!("取消工作流实例失败: {}", e)
            }
        }))),
    }
}

/// 删除工作流实例
pub async fn delete_workflow_instance(
    path: web::Path<String>,
    workflow_service: web::Data<Arc<WorkflowService>>,
) -> Result<HttpResponse> {
    let instance_id = path.into_inner();

    match workflow_service
        .delete_workflow_instance(&instance_id)
        .await
    {
        Ok(_) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": {
                "instance_id": instance_id,
                "message": "工作流实例已删除"
            }
        }))),
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DELETE_ERROR",
                "message": format!("删除工作流实例失败: {}", e)
            }
        }))),
    }
}

/// 获取工作流统计信息
pub async fn get_workflow_statistics(
    workflow_service: web::Data<Arc<WorkflowService>>,
) -> Result<HttpResponse> {
    match workflow_service.get_workflow_statistics().await {
        Ok(stats) => Ok(HttpResponse::Ok().json(json!({
            "success": true,
            "data": stats
        }))),
        Err(e) => Ok(HttpResponse::InternalServerError().json(json!({
            "success": false,
            "error": {
                "code": "DATABASE_ERROR",
                "message": format!("获取统计信息失败: {}", e)
            }
        }))),
    }
}
