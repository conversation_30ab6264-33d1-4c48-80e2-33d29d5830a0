//! 扫描工具集成模块
//!
//! 集成各种安全扫描工具

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

pub mod crawl4ai;
pub mod dnsx;
pub mod httpx;
pub mod manager;
pub mod nmap;
pub mod nuclei;
pub mod results;
pub mod subfinder;
pub mod workflow;

/// 扫描工具执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolResult {
    pub success: bool,
    pub output: String,
    pub error: Option<String>,
    pub data: Option<serde_json::Value>,
    pub execution_time: u64, // 毫秒
}

/// 扫描工具配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolConfig {
    pub timeout: Option<u64>,
    pub threads: Option<u32>,
    pub rate_limit: Option<u32>,
    pub custom_args: Option<Vec<String>>,
    pub output_format: Option<String>,
}

impl Default for ToolConfig {
    fn default() -> Self {
        Self {
            timeout: Some(30000), // 30秒
            threads: Some(10),
            rate_limit: Some(100),
            custom_args: None,
            output_format: Some("json".to_string()),
        }
    }
}

/// 通用扫描工具接口
#[async_trait]
pub trait ScanTool: Send + Sync {
    /// 工具名称
    fn name(&self) -> &'static str;

    /// 工具版本
    fn version(&self) -> &'static str;

    /// 检查工具是否可用
    async fn is_available(&self) -> bool;

    /// 执行扫描
    async fn scan(
        &self,
        target: &str,
        config: &ToolConfig,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>>;

    /// 解析输出结果
    fn parse_output(
        &self,
        output: &str,
    ) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>>;

    /// 获取默认配置
    fn default_config(&self) -> ToolConfig {
        ToolConfig::default()
    }
}
