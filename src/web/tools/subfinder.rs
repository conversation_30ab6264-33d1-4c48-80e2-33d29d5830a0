//! Subfinder 工具集成

use super::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use async_trait::async_trait;
use serde_json::json;
use std::time::Instant;
use tokio::process::Command;

/// Subfinder 工具
pub struct SubfinderTool;

impl SubfinderTool {
    pub fn new() -> Self {
        Self
    }

    /// 构建 Subfinder 命令
    fn build_command(&self, target: &str, config: &ToolConfig) -> Command {
        let mut cmd = Command::new("subfinder");

        // 基础参数
        cmd.arg("-silent");
        cmd.arg("-json");

        // 添加目标域名
        cmd.arg("-d").arg(target);

        // 添加配置参数
        if let Some(threads) = config.threads {
            cmd.arg("-t").arg(threads.to_string());
        }

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg((timeout / 1000).to_string());
        }

        // 添加自定义参数
        if let Some(custom_args) = &config.custom_args {
            for arg in custom_args {
                cmd.arg(arg);
            }
        }

        cmd
    }
}

#[async_trait]
impl ScanTool for SubfinderTool {
    fn name(&self) -> &'static str {
        "subfinder"
    }

    fn version(&self) -> &'static str {
        "2.6.0"
    }

    async fn is_available(&self) -> bool {
        Command::new("subfinder")
            .arg("-version")
            .output()
            .await
            .map(|output| output.status.success())
            .unwrap_or(false)
    }

    async fn scan(
        &self,
        target: &str,
        config: &ToolConfig,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = Instant::now();

        // 构建命令
        let mut cmd = self.build_command(target, config);

        // 执行命令
        let output = cmd.output().await?;
        let execution_time = start_time.elapsed().as_millis() as u64;

        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        let stderr = String::from_utf8_lossy(&output.stderr).to_string();

        let success = output.status.success();
        let error = if !stderr.is_empty() && !success {
            Some(stderr)
        } else {
            None
        };

        // 解析输出
        let data = if success && !stdout.is_empty() {
            self.parse_output(&stdout).ok()
        } else {
            None
        };

        Ok(ToolResult {
            success,
            output: stdout,
            error,
            data,
            execution_time,
        })
    }

    fn parse_output(
        &self,
        output: &str,
    ) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let mut subdomains = Vec::new();

        for line in output.lines() {
            if line.trim().is_empty() {
                continue;
            }

            // 尝试解析 JSON 格式的输出
            if let Ok(json_record) = serde_json::from_str::<serde_json::Value>(line) {
                subdomains.push(json_record);
            } else {
                // 解析文本格式的输出（每行一个子域名）
                subdomains.push(json!({
                    "host": line.trim(),
                    "source": "subfinder"
                }));
            }
        }

        Ok(json!({
            "subdomains": subdomains,
            "count": subdomains.len(),
            "scan_type": "subdomain_enumeration",
            "tool": "subfinder"
        }))
    }

    fn default_config(&self) -> ToolConfig {
        ToolConfig {
            timeout: Some(30000), // 30秒
            threads: Some(10),
            rate_limit: Some(100),
            custom_args: Some(vec!["-all".to_string()]), // 使用所有数据源
            output_format: Some("json".to_string()),
        }
    }
}
