//! 扫描工具管理器

use super::dnsx::DnsxTool;
use super::httpx::HttpxTool;
use super::nmap::RustScanTool;
use super::nuclei::NucleiTool;
use super::subfinder::SubfinderTool;
use super::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>on<PERSON>g, ToolResult};
use std::collections::HashMap;
use std::sync::Arc;

/// 扫描工具管理器
pub struct ToolManager {
    tools: HashMap<String, Arc<dyn ScanTool>>,
}

impl ToolManager {
    /// 创建新的工具管理器
    pub fn new() -> Self {
        let mut tools: HashMap<String, Arc<dyn ScanTool>> = HashMap::new();

        // 注册所有工具
        tools.insert("rustscan".to_string(), Arc::new(RustScanTool::new()));
        tools.insert("dnsx".to_string(), Arc::new(DnsxTool::new()));
        tools.insert("subfinder".to_string(), Arc::new(SubfinderTool::new()));
        tools.insert("httpx".to_string(), Arc::new(HttpxTool::new()));
        tools.insert("nuclei".to_string(), Arc::new(NucleiTool::new()));

        Self { tools }
    }

    /// 获取工具
    pub fn get_tool(&self, name: &str) -> Option<Arc<dyn ScanTool>> {
        self.tools.get(name).cloned()
    }

    /// 获取所有工具名称
    pub fn get_tool_names(&self) -> Vec<String> {
        self.tools.keys().cloned().collect()
    }

    /// 检查工具是否可用
    pub async fn is_tool_available(&self, name: &str) -> bool {
        if let Some(tool) = self.get_tool(name) {
            tool.is_available().await
        } else {
            false
        }
    }

    /// 执行扫描
    pub async fn scan(
        &self,
        tool_name: &str,
        target: &str,
        config: Option<ToolConfig>,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let tool = self
            .get_tool(tool_name)
            .ok_or_else(|| format!("工具 '{}' 不存在", tool_name))?;

        let config = config.unwrap_or_else(|| tool.default_config());

        tool.scan(target, &config).await
    }

    /// 获取工具信息
    pub async fn get_tool_info(&self, name: &str) -> Option<ToolInfo> {
        if let Some(tool) = self.get_tool(name) {
            let available = tool.is_available().await;
            Some(ToolInfo {
                name: tool.name().to_string(),
                version: tool.version().to_string(),
                available,
                default_config: tool.default_config(),
            })
        } else {
            None
        }
    }

    /// 获取所有工具信息
    pub async fn get_all_tools_info(&self) -> Vec<ToolInfo> {
        let mut tools_info = Vec::new();

        for name in self.get_tool_names() {
            if let Some(info) = self.get_tool_info(&name).await {
                tools_info.push(info);
            }
        }

        tools_info
    }
}

impl Default for ToolManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 工具信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ToolInfo {
    pub name: String,
    pub version: String,
    pub available: bool,
    pub default_config: ToolConfig,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_tool_manager_creation() {
        let manager = ToolManager::new();
        let tool_names = manager.get_tool_names();

        assert!(tool_names.contains(&"rustscan".to_string()));
        assert!(tool_names.contains(&"dnsx".to_string()));
        assert!(tool_names.contains(&"subfinder".to_string()));
        assert!(tool_names.contains(&"httpx".to_string()));
        assert!(tool_names.contains(&"nuclei".to_string()));
    }

    #[tokio::test]
    async fn test_get_tool() {
        let manager = ToolManager::new();

        assert!(manager.get_tool("rustscan").is_some());
        assert!(manager.get_tool("nonexistent").is_none());
    }

    #[tokio::test]
    async fn test_tool_info() {
        let manager = ToolManager::new();

        if let Some(info) = manager.get_tool_info("rustscan").await {
            assert_eq!(info.name, "rustscan");
            assert!(!info.version.is_empty());
        }
    }
}
