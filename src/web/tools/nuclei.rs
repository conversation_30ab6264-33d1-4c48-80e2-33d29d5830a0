//! Nuclei 工具集成

use super::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use async_trait::async_trait;
use serde_json::json;
use std::time::Instant;
use tokio::process::Command;

/// Nuclei 工具
pub struct NucleiTool;

impl NucleiTool {
    pub fn new() -> Self {
        Self
    }

    /// 构建 Nuclei 命令
    fn build_command(&self, target: &str, config: &ToolConfig) -> Command {
        let mut cmd = Command::new("nuclei");

        // 基础参数
        cmd.arg("-silent");
        cmd.arg("-json");

        // 添加目标
        cmd.arg("-u").arg(target);

        // 添加配置参数
        if let Some(threads) = config.threads {
            cmd.arg("-c").arg(threads.to_string());
        }

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg((timeout / 1000).to_string());
        }

        if let Some(rate_limit) = config.rate_limit {
            cmd.arg("-rate-limit").arg(rate_limit.to_string());
        }

        // 添加自定义参数
        if let Some(custom_args) = &config.custom_args {
            for arg in custom_args {
                cmd.arg(arg);
            }
        }

        cmd
    }
}

#[async_trait]
impl ScanTool for NucleiTool {
    fn name(&self) -> &'static str {
        "nuclei"
    }

    fn version(&self) -> &'static str {
        "3.0.0"
    }

    async fn is_available(&self) -> bool {
        Command::new("nuclei")
            .arg("-version")
            .output()
            .await
            .map(|output| output.status.success())
            .unwrap_or(false)
    }

    async fn scan(
        &self,
        target: &str,
        config: &ToolConfig,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = Instant::now();

        // 构建命令
        let mut cmd = self.build_command(target, config);

        // 执行命令
        let output = cmd.output().await?;
        let execution_time = start_time.elapsed().as_millis() as u64;

        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        let stderr = String::from_utf8_lossy(&output.stderr).to_string();

        let success = output.status.success();
        let error = if !stderr.is_empty() && !success {
            Some(stderr)
        } else {
            None
        };

        // 解析输出
        let data = if success && !stdout.is_empty() {
            self.parse_output(&stdout).ok()
        } else {
            None
        };

        Ok(ToolResult {
            success,
            output: stdout,
            error,
            data,
            execution_time,
        })
    }

    fn parse_output(
        &self,
        output: &str,
    ) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let mut vulnerabilities = Vec::new();

        for line in output.lines() {
            if line.trim().is_empty() {
                continue;
            }

            // 尝试解析 JSON 格式的输出
            if let Ok(json_record) = serde_json::from_str::<serde_json::Value>(line) {
                vulnerabilities.push(json_record);
            }
        }

        // 按严重程度分类
        let mut critical = 0;
        let mut high = 0;
        let mut medium = 0;
        let mut low = 0;
        let mut info = 0;

        for vuln in &vulnerabilities {
            if let Some(severity) = vuln
                .get("info")
                .and_then(|i| i.get("severity"))
                .and_then(|s| s.as_str())
            {
                match severity.to_lowercase().as_str() {
                    "critical" => critical += 1,
                    "high" => high += 1,
                    "medium" => medium += 1,
                    "low" => low += 1,
                    _ => info += 1,
                }
            }
        }

        Ok(json!({
            "vulnerabilities": vulnerabilities,
            "count": vulnerabilities.len(),
            "severity_stats": {
                "critical": critical,
                "high": high,
                "medium": medium,
                "low": low,
                "info": info
            },
            "scan_type": "vulnerability_scan",
            "tool": "nuclei"
        }))
    }

    fn default_config(&self) -> ToolConfig {
        ToolConfig {
            timeout: Some(60000), // 60秒
            threads: Some(25),
            rate_limit: Some(150),
            custom_args: Some(vec![
                "-severity".to_string(),
                "critical,high,medium".to_string(),
                "-exclude-tags".to_string(),
                "dos".to_string(),
            ]),
            output_format: Some("json".to_string()),
        }
    }
}
