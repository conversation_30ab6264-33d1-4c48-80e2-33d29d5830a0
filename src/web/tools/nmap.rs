//! RustScan 端口扫描工具集成

use super::{<PERSON>anT<PERSON>, Tool<PERSON>onfig, ToolResult};
use async_trait::async_trait;
use serde_json::json;
use std::process::Command;
use std::time::Instant;
use tokio::process::Command as AsyncCommand;

/// RustScan 工具（基于现有的 RustScan 功能）
pub struct RustScanTool;

impl RustScanTool {
    pub fn new() -> Self {
        Self
    }

    /// 构建 RustScan 命令
    fn build_command(&self, target: &str, config: &ToolConfig) -> AsyncCommand {
        let mut cmd = AsyncCommand::new("rustscan");

        // 添加目标
        cmd.arg("-a").arg(target);

        // 添加配置参数
        if let Some(timeout) = config.timeout {
            cmd.arg("-t").arg((timeout / 1000).to_string()); // 转换为秒
        }

        if let Some(threads) = config.threads {
            cmd.arg("--batch-size").arg(threads.to_string());
        }

        // 输出格式
        cmd.arg("--format").arg("json");

        // 添加自定义参数
        if let Some(custom_args) = &config.custom_args {
            for arg in custom_args {
                cmd.arg(arg);
            }
        }

        cmd
    }
}

#[async_trait]
impl ScanTool for RustScanTool {
    fn name(&self) -> &'static str {
        "rustscan"
    }

    fn version(&self) -> &'static str {
        env!("CARGO_PKG_VERSION")
    }

    async fn is_available(&self) -> bool {
        // RustScan 是内置的，总是可用
        true
    }

    async fn scan(
        &self,
        target: &str,
        config: &ToolConfig,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = Instant::now();

        // 构建命令
        let mut cmd = self.build_command(target, config);

        // 执行命令
        let output = cmd.output().await?;
        let execution_time = start_time.elapsed().as_millis() as u64;

        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        let stderr = String::from_utf8_lossy(&output.stderr).to_string();

        let success = output.status.success();
        let error = if !stderr.is_empty() && !success {
            Some(stderr)
        } else {
            None
        };

        // 解析输出
        let data = if success && !stdout.is_empty() {
            self.parse_output(&stdout).ok()
        } else {
            None
        };

        Ok(ToolResult {
            success,
            output: stdout,
            error,
            data,
            execution_time,
        })
    }

    fn parse_output(
        &self,
        output: &str,
    ) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        // 尝试解析 JSON 输出
        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(output) {
            return Ok(json_value);
        }

        // 如果不是 JSON，解析文本输出
        let mut ports = Vec::new();
        for line in output.lines() {
            if line.contains("Open") {
                // 解析端口信息，格式类似: "Open 192.168.1.1:22"
                if let Some(port_info) = line.split_whitespace().nth(1) {
                    if let Some(port) = port_info.split(':').nth(1) {
                        if let Ok(port_num) = port.parse::<u16>() {
                            ports.push(json!({
                                "port": port_num,
                                "status": "open",
                                "protocol": "tcp"
                            }));
                        }
                    }
                }
            }
        }

        Ok(json!({
            "ports": ports,
            "scan_type": "port_scan",
            "tool": "rustscan"
        }))
    }

    fn default_config(&self) -> ToolConfig {
        ToolConfig {
            timeout: Some(5000), // 5秒
            threads: Some(1000), // RustScan 默认批处理大小
            rate_limit: None,
            custom_args: None,
            output_format: Some("json".to_string()),
        }
    }
}
