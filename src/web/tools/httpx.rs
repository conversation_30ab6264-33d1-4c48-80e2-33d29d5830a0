//! HTTPx 工具集成

use super::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onfi<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use async_trait::async_trait;
use serde_json::json;
use std::time::Instant;
use tokio::process::Command;

/// HTTPx 工具
pub struct HttpxTool;

impl HttpxTool {
    pub fn new() -> Self {
        Self
    }

    /// 构建 HTTPx 命令
    fn build_command(&self, target: &str, config: &ToolConfig) -> Command {
        let mut cmd = Command::new("httpx");

        // 基础参数
        cmd.arg("-silent");
        cmd.arg("-json");

        // 添加目标
        cmd.arg("-u").arg(target);

        // 添加配置参数
        if let Some(threads) = config.threads {
            cmd.arg("-threads").arg(threads.to_string());
        }

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg((timeout / 1000).to_string());
        }

        if let Some(rate_limit) = config.rate_limit {
            cmd.arg("-rate-limit").arg(rate_limit.to_string());
        }

        // 添加自定义参数
        if let Some(custom_args) = &config.custom_args {
            for arg in custom_args {
                cmd.arg(arg);
            }
        }

        cmd
    }
}

#[async_trait]
impl ScanTool for HttpxTool {
    fn name(&self) -> &'static str {
        "httpx"
    }

    fn version(&self) -> &'static str {
        "1.3.0"
    }

    async fn is_available(&self) -> bool {
        Command::new("httpx")
            .arg("-version")
            .output()
            .await
            .map(|output| output.status.success())
            .unwrap_or(false)
    }

    async fn scan(
        &self,
        target: &str,
        config: &ToolConfig,
    ) -> Result<ToolResult, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = Instant::now();

        // 构建命令
        let mut cmd = self.build_command(target, config);

        // 执行命令
        let output = cmd.output().await?;
        let execution_time = start_time.elapsed().as_millis() as u64;

        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        let stderr = String::from_utf8_lossy(&output.stderr).to_string();

        let success = output.status.success();
        let error = if !stderr.is_empty() && !success {
            Some(stderr)
        } else {
            None
        };

        // 解析输出
        let data = if success && !stdout.is_empty() {
            self.parse_output(&stdout).ok()
        } else {
            None
        };

        Ok(ToolResult {
            success,
            output: stdout,
            error,
            data,
            execution_time,
        })
    }

    fn parse_output(
        &self,
        output: &str,
    ) -> Result<serde_json::Value, Box<dyn std::error::Error + Send + Sync>> {
        let mut web_services = Vec::new();

        for line in output.lines() {
            if line.trim().is_empty() {
                continue;
            }

            // 尝试解析 JSON 格式的输出
            if let Ok(json_record) = serde_json::from_str::<serde_json::Value>(line) {
                web_services.push(json_record);
            } else {
                // 解析文本格式的输出
                web_services.push(json!({
                    "url": line.trim(),
                    "status": "alive"
                }));
            }
        }

        Ok(json!({
            "web_services": web_services,
            "count": web_services.len(),
            "scan_type": "web_probe",
            "tool": "httpx"
        }))
    }

    fn default_config(&self) -> ToolConfig {
        ToolConfig {
            timeout: Some(10000), // 10秒
            threads: Some(50),
            rate_limit: Some(150),
            custom_args: Some(vec![
                "-status-code".to_string(),
                "-title".to_string(),
                "-tech-detect".to_string(),
                "-follow-redirects".to_string(),
            ]),
            output_format: Some("json".to_string()),
        }
    }
}
