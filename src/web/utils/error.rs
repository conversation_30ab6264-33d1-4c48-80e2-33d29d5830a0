//! 错误处理模块
//!
//! 定义应用程序的错误类型和处理逻辑

use actix_web::{HttpResponse, ResponseError};
use serde_json::json;
use thiserror::Error;

/// 应用程序错误类型
#[derive(Error, Debug)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),

    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("验证错误: {0}")]
    Validation(String),

    #[error("未找到资源: {0}")]
    NotFound(String),

    #[error("权限不足")]
    Unauthorized,

    #[error("内部服务器错误: {0}")]
    Internal(String),

    #[error("外部工具错误: {0}")]
    ExternalTool(String),
}

impl ResponseError for AppError {
    fn error_response(&self) -> HttpResponse {
        let (status, error_code, message) = match self {
            AppError::Database(_) => (
                actix_web::http::StatusCode::INTERNAL_SERVER_ERROR,
                "DATABASE_ERROR",
                "数据库操作失败",
            ),
            AppError::Serialization(_) => (
                actix_web::http::StatusCode::BAD_REQUEST,
                "SERIALIZATION_ERROR",
                "数据序列化失败",
            ),
            AppError::Validation(msg) => (
                actix_web::http::StatusCode::BAD_REQUEST,
                "VALIDATION_ERROR",
                msg.as_str(),
            ),
            AppError::NotFound(msg) => (
                actix_web::http::StatusCode::NOT_FOUND,
                "NOT_FOUND",
                msg.as_str(),
            ),
            AppError::Unauthorized => (
                actix_web::http::StatusCode::UNAUTHORIZED,
                "UNAUTHORIZED",
                "权限不足",
            ),
            AppError::Internal(_) => (
                actix_web::http::StatusCode::INTERNAL_SERVER_ERROR,
                "INTERNAL_ERROR",
                "内部服务器错误",
            ),
            AppError::ExternalTool(_) => (
                actix_web::http::StatusCode::INTERNAL_SERVER_ERROR,
                "EXTERNAL_TOOL_ERROR",
                "外部工具执行失败",
            ),
        };

        HttpResponse::build(status).json(json!({
            "error": {
                "code": error_code,
                "message": message,
                "details": self.to_string()
            }
        }))
    }
}

/// 应用程序结果类型
pub type AppResult<T> = Result<T, AppError>;
