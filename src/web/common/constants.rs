//! 系统常量定义

/// 默认配置常量
pub mod defaults {
    pub const DEFAULT_TIMEOUT: u64 = 5000; // 5秒
    pub const DEFAULT_BATCH_SIZE: u16 = 1000;
    pub const MAX_CONCURRENT_TASKS: usize = 5;
    pub const DEFAULT_PAGE_SIZE: u32 = 20;
    pub const MAX_PAGE_SIZE: u32 = 100;
}

/// 扫描工具名称
pub mod tools {
    pub const RUSTSCAN: &str = "rustscan";
    pub const NMAP: &str = "nmap";
    pub const DNSX: &str = "dnsx";
    pub const SUBFINDER: &str = "subfinder";
    pub const HTTPX: &str = "httpx";
    pub const CRAWL4AI: &str = "crawl4ai";
    pub const NUCLEI: &str = "nuclei";
}

/// HTTP 状态码
pub mod http_status {
    pub const OK: u16 = 200;
    pub const CREATED: u16 = 201;
    pub const BAD_REQUEST: u16 = 400;
    pub const UNAUTHORIZED: u16 = 401;
    pub const NOT_FOUND: u16 = 404;
    pub const INTERNAL_SERVER_ERROR: u16 = 500;
}

/// 任务状态
pub mod task_status {
    pub const PENDING: &str = "pending";
    pub const RUNNING: &str = "running";
    pub const COMPLETED: &str = "completed";
    pub const FAILED: &str = "failed";
    pub const CANCELLED: &str = "cancelled";
}
