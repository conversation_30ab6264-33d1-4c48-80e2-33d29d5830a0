//! User-Agent 随机化系统

/// User-Agent 类型
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum UserAgentType {
    Chrome,
    Firefox,
    Safari,
    Edge,
    Mobile,
    Bot,
}

/// User-Agent 生成器
pub struct UserAgentGenerator {
    chrome_agents: Vec<&'static str>,
    firefox_agents: Vec<&'static str>,
    safari_agents: Vec<&'static str>,
    edge_agents: Vec<&'static str>,
    mobile_agents: Vec<&'static str>,
    bot_agents: Vec<&'static str>,
}

impl UserAgentGenerator {
    pub fn new() -> Self {
        Self {
            chrome_agents: vec![
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            ],
            firefox_agents: vec![
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
            ],
            safari_agents: vec![
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
            ],
            edge_agents: vec![
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            ],
            mobile_agents: vec![
                "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
            ],
            bot_agents: vec![
                "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
                "Mozilla/5.0 (compatible; Bingbot/2.0; +http://www.bing.com/bingbot.htm)",
            ],
        }
    }

    /// 获取默认 User-Agent (Chrome)
    pub fn default(&self) -> &'static str {
        self.chrome_agents[0]
    }

    /// 根据类型获取默认 User-Agent
    pub fn default_by_type(&self, ua_type: UserAgentType) -> &'static str {
        match ua_type {
            UserAgentType::Chrome => self.chrome_agents[0],
            UserAgentType::Firefox => self.firefox_agents[0],
            UserAgentType::Safari => self.safari_agents[0],
            UserAgentType::Edge => self.edge_agents[0],
            UserAgentType::Mobile => self.mobile_agents[0],
            UserAgentType::Bot => self.bot_agents[0],
        }
    }
}

impl Default for UserAgentGenerator {
    fn default() -> Self {
        Self::new()
    }
}
