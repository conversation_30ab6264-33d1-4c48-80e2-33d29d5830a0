import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import WorkflowTemplates from './components/WorkflowTemplates';
import WorkflowInstances from './components/WorkflowInstances';
import WorkflowDetail from './components/WorkflowDetail';
import CreateWorkflow from './components/CreateWorkflow';
import { NotificationProvider } from './components/NotificationSystem';

function App() {
  return (
    <NotificationProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/templates" element={<WorkflowTemplates />} />
            <Route path="/instances" element={<WorkflowInstances />} />
            <Route path="/instances/:id" element={<WorkflowDetail />} />
            <Route path="/create" element={<CreateWorkflow />} />
          </Routes>
        </Layout>
      </Router>
    </NotificationProvider>
  );
}

export default App;
