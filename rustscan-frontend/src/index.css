@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@layer base {
  :root {
    /* 专业网络安全主题 - 深色为主 */
    --background: 220 13% 9%;
    --foreground: 220 9% 95%;
    --card: 220 13% 11%;
    --card-foreground: 220 9% 95%;
    --popover: 220 13% 11%;
    --popover-foreground: 220 9% 95%;
    --primary: 142 76% 36%;
    --primary-foreground: 220 13% 9%;
    --secondary: 220 13% 15%;
    --secondary-foreground: 220 9% 95%;
    --muted: 220 13% 15%;
    --muted-foreground: 220 9% 65%;
    --accent: 220 13% 15%;
    --accent-foreground: 220 9% 95%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 220 9% 95%;
    --warning: 38 92% 50%;
    --warning-foreground: 220 13% 9%;
    --success: 142 76% 36%;
    --success-foreground: 220 13% 9%;
    --info: 217 91% 60%;
    --info-foreground: 220 13% 9%;
    --border: 220 13% 18%;
    --input: 220 13% 18%;
    --ring: 142 76% 36%;
    --radius: 0.75rem;

    /* 网络安全特色颜色 */
    --cyber-green: 142 76% 36%;
    --cyber-blue: 217 91% 60%;
    --cyber-purple: 271 81% 56%;
    --cyber-orange: 25 95% 53%;
    --cyber-red: 0 84% 60%;
    --terminal-green: 120 100% 50%;
    --matrix-green: 120 100% 25%;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142 76% 36%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-sm {
    @apply h-9 px-3;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-11 px-8;
  }

  .btn-primary {
    @apply btn btn-md bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm;
  }

  .btn-secondary {
    @apply btn btn-md bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm;
  }

  .btn-outline {
    @apply btn btn-md border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-sm;
  }

  .btn-ghost {
    @apply btn btn-md hover:bg-accent hover:text-accent-foreground;
  }

  .btn-destructive {
    @apply btn btn-md bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm;
  }

  /* 卡片组件 */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* 输入组件 */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* 标签组件 */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply badge border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }

  .badge-secondary {
    @apply badge border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-destructive {
    @apply badge border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }

  .badge-outline {
    @apply badge text-foreground;
  }

  .badge-success {
    @apply badge border-transparent bg-green-100 text-green-800 hover:bg-green-100/80;
  }

  .badge-warning {
    @apply badge border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80;
  }

  .badge-info {
    @apply badge border-transparent bg-blue-100 text-blue-800 hover:bg-blue-100/80;
  }

  /* 网络安全专业组件 */
  .cyber-card {
    @apply card border-border/50 bg-gradient-to-br from-card to-card/80 backdrop-blur-sm;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .cyber-card:hover {
    @apply border-primary/30;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .terminal-card {
    @apply bg-black/90 border border-green-500/30 rounded-lg font-mono text-green-400;
    background-image:
      linear-gradient(90deg, transparent 79px, rgba(34, 197, 94, 0.03) 81px, rgba(34, 197, 94, 0.03) 82px, transparent 84px),
      linear-gradient(rgba(34, 197, 94, 0.03) 50%, transparent 50%);
    background-size: 84px 2em;
  }

  .scan-progress {
    @apply relative overflow-hidden rounded-full bg-secondary;
  }

  .scan-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%;
    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.6), transparent);
    animation: scan-line 2s linear infinite;
  }

  .status-indicator {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium;
  }

  .status-running {
    @apply status-indicator bg-blue-500/10 text-blue-400 border border-blue-500/20;
  }

  .status-completed {
    @apply status-indicator bg-green-500/10 text-green-400 border border-green-500/20;
  }

  .status-failed {
    @apply status-indicator bg-red-500/10 text-red-400 border border-red-500/20;
  }

  .status-pending {
    @apply status-indicator bg-yellow-500/10 text-yellow-400 border border-yellow-500/20;
  }

  .metric-card {
    @apply cyber-card p-6 hover:scale-105 transition-all duration-300;
  }

  .metric-value {
    @apply text-3xl font-bold bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent;
  }

  .cyber-button {
    @apply btn relative overflow-hidden;
  }

  .cyber-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.2), transparent);
    transition: left 0.5s;
  }

  .cyber-button:hover::before {
    left: 100%;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 专业动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 5px hsl(var(--success));
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 20px hsl(var(--success)), 0 0 30px hsl(var(--success));
  }
}

@keyframes scan-line {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes matrix-rain {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

@keyframes cyber-glow {
  0%, 100% {
    text-shadow: 0 0 5px hsl(var(--cyber-green)), 0 0 10px hsl(var(--cyber-green));
  }
  50% {
    text-shadow: 0 0 10px hsl(var(--cyber-green)), 0 0 20px hsl(var(--cyber-green)), 0 0 30px hsl(var(--cyber-green));
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-scan-line {
  animation: scan-line 2s linear infinite;
}

.animate-cyber-glow {
  animation: cyber-glow 2s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 工作流步骤连接线 */
.workflow-line {
  position: relative;
}

.workflow-line::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -20px;
  width: 40px;
  height: 2px;
  background: #e5e7eb;
  transform: translateY(-50%);
}

.workflow-line.active::after {
  background: #3b82f6;
}

.workflow-line.completed::after {
  background: #22c55e;
}
