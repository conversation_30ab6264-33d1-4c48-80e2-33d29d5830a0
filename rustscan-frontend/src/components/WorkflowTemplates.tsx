import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  FileText,
  Play,
  Eye,
  ChevronRight,
  Loader2
} from 'lucide-react';
import { workflowApi, WorkflowDefinition } from '../services/api';

const WorkflowTemplates: React.FC = () => {
  const [templates, setTemplates] = useState<WorkflowDefinition[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowDefinition | null>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const response = await workflowApi.getWorkflowTemplates();
      if (response.success && response.data) {
        setTemplates(response.data.templates);
      } else {
        setError(response.error?.message || '加载模板失败');
      }
    } catch (err) {
      setError('网络错误，请检查连接');
    } finally {
      setLoading(false);
    }
  };

  const getToolIcon = (toolName: string) => {
    const icons: Record<string, string> = {
      rustscan: '🔍',
      nmap: '🌐',
      subfinder: '🔎',
      dnsx: '🌍',
      httpx: '🌐',
      nuclei: '🛡️'
    };
    return icons[toolName] || '⚡';
  };

  const getStepStatusColor = (index: number, totalSteps: number) => {
    if (index === 0) return 'bg-primary-100 text-primary-700 border-primary-200';
    if (index === totalSteps - 1) return 'bg-success-100 text-success-700 border-success-200';
    return 'bg-gray-100 text-gray-700 border-gray-200';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
          <span className="ml-2 text-gray-600">加载工作流模板...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button onClick={loadTemplates} className="btn-primary">
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">工作流模板</h1>
          <p className="text-gray-600">选择预定义的工作流模板来快速开始扫描</p>
        </div>
        <Link to="/create" className="btn-primary">
          <Play className="h-4 w-4 mr-2" />
          创建实例
        </Link>
      </div>

      {/* 模板列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {templates.map((template) => (
          <div key={template.id} className="card hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 rounded-lg mr-3">
                  <FileText className="h-6 w-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{template.name}</h3>
                  <p className="text-sm text-gray-600">版本 {template.version}</p>
                </div>
              </div>
              <span className="badge-info">{template.steps.length} 步骤</span>
            </div>

            <p className="text-gray-600 mb-4">{template.description}</p>

            {/* 工作流步骤预览 */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">执行步骤</h4>
              <div className="space-y-2">
                {template.steps.slice(0, 3).map((step, index) => (
                  <div key={step.id} className="flex items-center text-sm">
                    <span className="mr-2">{getToolIcon(step.tool)}</span>
                    <span className="text-gray-700">{step.name}</span>
                    <span className="ml-auto text-xs text-gray-500">{step.tool}</span>
                  </div>
                ))}
                {template.steps.length > 3 && (
                  <div className="text-sm text-gray-500">
                    ... 还有 {template.steps.length - 3} 个步骤
                  </div>
                )}
              </div>
            </div>

            {/* 全局配置 */}
            {template.global_config && (
              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">配置参数</h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {Object.entries(template.global_config).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600">{key}:</span>
                      <span className="text-gray-900">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <button
                onClick={() => setSelectedTemplate(template)}
                className="flex items-center text-sm text-primary-600 hover:text-primary-700"
              >
                <Eye className="h-4 w-4 mr-1" />
                查看详情
              </button>
              <Link
                to={`/create?template=${template.id}`}
                className="btn-primary text-sm"
              >
                使用模板
                <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* 模板详情模态框 */}
      {selectedTemplate && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div 
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
              onClick={() => setSelectedTemplate(null)}
            />
            
            <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {selectedTemplate.name}
                </h3>
                <button
                  onClick={() => setSelectedTemplate(null)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">描述</h4>
                  <p className="text-gray-600">{selectedTemplate.description}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">执行步骤</h4>
                  <div className="space-y-3">
                    {selectedTemplate.steps.map((step, index) => (
                      <div key={step.id} className="flex items-start p-3 border border-gray-200 rounded-lg">
                        <div className={`
                          flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium mr-3
                          ${getStepStatusColor(index, selectedTemplate.steps.length)}
                        `}>
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <h5 className="font-medium text-gray-900">{step.name}</h5>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                              {step.tool}
                            </span>
                          </div>
                          {step.depends_on.length > 0 && (
                            <p className="text-xs text-gray-500 mb-2">
                              依赖: {step.depends_on.join(', ')}
                            </p>
                          )}
                          {Object.keys(step.parameters).length > 0 && (
                            <div className="text-xs text-gray-600">
                              参数: {Object.entries(step.parameters).map(([k, v]) => `${k}=${v}`).join(', ')}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {selectedTemplate.global_config && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">全局配置</h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <pre className="text-sm text-gray-700">
                        {JSON.stringify(selectedTemplate.global_config, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setSelectedTemplate(null)}
                  className="btn-secondary"
                >
                  关闭
                </button>
                <Link
                  to={`/create?template=${selectedTemplate.id}`}
                  className="btn-primary"
                  onClick={() => setSelectedTemplate(null)}
                >
                  使用此模板
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkflowTemplates;
