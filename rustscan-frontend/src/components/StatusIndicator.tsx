import React from 'react';
import { 
  Check<PERSON>ircle, 
  XCircle, 
  Clock, 
  Loader2, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  Pause,
  Play,
  Zap
} from 'lucide-react';

interface StatusIndicatorProps {
  status: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  animated?: boolean;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ 
  status, 
  size = 'md', 
  showText = true,
  animated = true 
}) => {
  const getStatusConfig = (status: string) => {
    const configs: Record<string, any> = {
      'completed': {
        icon: CheckCircle,
        text: '已完成',
        className: 'status-completed',
        iconColor: 'text-green-400',
        bgColor: 'bg-green-500/10',
        borderColor: 'border-green-500/20',
        glowColor: 'shadow-green-500/20'
      },
      'running': {
        icon: animated ? Loader2 : Play,
        text: '运行中',
        className: 'status-running',
        iconColor: 'text-blue-400',
        bgColor: 'bg-blue-500/10',
        borderColor: 'border-blue-500/20',
        glowColor: 'shadow-blue-500/20',
        animate: animated ? 'animate-spin' : ''
      },
      'failed': {
        icon: XCircle,
        text: '失败',
        className: 'status-failed',
        iconColor: 'text-red-400',
        bgColor: 'bg-red-500/10',
        borderColor: 'border-red-500/20',
        glowColor: 'shadow-red-500/20'
      },
      'pending': {
        icon: Clock,
        text: '等待中',
        className: 'status-pending',
        iconColor: 'text-yellow-400',
        bgColor: 'bg-yellow-500/10',
        borderColor: 'border-yellow-500/20',
        glowColor: 'shadow-yellow-500/20'
      },
      'paused': {
        icon: Pause,
        text: '已暂停',
        className: 'status-indicator',
        iconColor: 'text-gray-400',
        bgColor: 'bg-gray-500/10',
        borderColor: 'border-gray-500/20',
        glowColor: 'shadow-gray-500/20'
      },
      'created': {
        icon: Zap,
        text: '已创建',
        className: 'status-indicator',
        iconColor: 'text-cyan-400',
        bgColor: 'bg-cyan-500/10',
        borderColor: 'border-cyan-500/20',
        glowColor: 'shadow-cyan-500/20'
      },
      'error': {
        icon: AlertTriangle,
        text: '错误',
        className: 'status-failed',
        iconColor: 'text-red-400',
        bgColor: 'bg-red-500/10',
        borderColor: 'border-red-500/20',
        glowColor: 'shadow-red-500/20'
      }
    };

    return configs[status.toLowerCase()] || configs['pending'];
  };

  const getSizeClasses = (size: string) => {
    const sizes: Record<string, any> = {
      'sm': {
        container: 'px-2 py-1 text-xs',
        icon: 'h-3 w-3',
        text: 'text-xs'
      },
      'md': {
        container: 'px-3 py-1.5 text-sm',
        icon: 'h-4 w-4',
        text: 'text-sm'
      },
      'lg': {
        container: 'px-4 py-2 text-base',
        icon: 'h-5 w-5',
        text: 'text-base'
      }
    };

    return sizes[size] || sizes['md'];
  };

  const config = getStatusConfig(status);
  const sizeClasses = getSizeClasses(size);
  const Icon = config.icon;

  return (
    <div className={`
      inline-flex items-center gap-2 rounded-full font-medium transition-all duration-300
      ${config.className}
      ${config.bgColor}
      ${config.borderColor}
      ${sizeClasses.container}
      border
      ${animated ? 'hover:scale-105' : ''}
      ${animated && config.glowColor ? `hover:shadow-lg ${config.glowColor}` : ''}
    `}>
      <Icon className={`
        ${sizeClasses.icon} 
        ${config.iconColor}
        ${config.animate || ''}
      `} />
      {showText && (
        <span className={`${sizeClasses.text} font-medium`}>
          {config.text}
        </span>
      )}
    </div>
  );
};

export default StatusIndicator;
