import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Activity,
  Eye,
  Plus,
  RefreshCw,
  Search,
  Pause,
  Trash2,
  RotateCcw
} from 'lucide-react';
import { workflowApi, WorkflowInstance, WorkflowProgress } from '../services/api';
import StatusIndicator from './StatusIndicator';
import ProgressBar from './ProgressBar';

const WorkflowInstances: React.FC = () => {
  const [instances, setInstances] = useState<WorkflowInstance[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadInstances();
  }, []);

  const loadInstances = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await workflowApi.getWorkflowInstances();
      if (response.success) {
        setInstances(response.data?.instances || []);
      } else {
        setError('获取工作流实例失败');
      }
    } catch (err) {
      console.error('加载工作流实例失败:', err);
      setError('网络错误，无法加载工作流实例');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'running':
        return <Activity className="h-5 w-5 text-yellow-500 animate-pulse" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'created':
      case 'pending':
        return <Clock className="h-5 w-5 text-gray-400" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-gray-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (status) {
      case 'completed':
        return <span className={`${baseClasses} bg-green-100 text-green-800`}>已完成</span>;
      case 'running':
        return <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>运行中</span>;
      case 'failed':
        return <span className={`${baseClasses} bg-red-100 text-red-800`}>失败</span>;
      case 'created':
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>已创建</span>;
      case 'pending':
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>等待中</span>;
      case 'cancelled':
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>已取消</span>;
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>未知</span>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const getDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return '-';
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.floor((end.getTime() - start.getTime()) / 1000);

    if (duration < 60) return `${duration}秒`;
    if (duration < 3600) return `${Math.floor(duration / 60)}分${duration % 60}秒`;
    return `${Math.floor(duration / 3600)}时${Math.floor((duration % 3600) / 60)}分`;
  };

  const getProgressPercentage = (progress?: number | WorkflowProgress): number => {
    if (!progress) return 0;
    if (typeof progress === 'number') return progress;
    return progress.percentage || 0;
  };

  // 操作处理函数
  const handleStopInstance = async (instanceId: string) => {
    if (!window.confirm('确定要停止这个扫描实例吗？')) return;

    try {
      // TODO: 实现停止实例的API调用
      console.log('停止实例:', instanceId);
      // await workflowApi.stopInstance(instanceId);
      // loadInstances(); // 重新加载列表
    } catch (error) {
      console.error('停止实例失败:', error);
    }
  };

  const handleRestartInstance = async (instanceId: string) => {
    if (!window.confirm('确定要重新运行这个扫描实例吗？')) return;

    try {
      // TODO: 实现重新运行实例的API调用
      console.log('重新运行实例:', instanceId);
      // await workflowApi.restartInstance(instanceId);
      // loadInstances(); // 重新加载列表
    } catch (error) {
      console.error('重新运行实例失败:', error);
    }
  };

  const handleDeleteInstance = async (instanceId: string) => {
    if (!window.confirm('确定要删除这个扫描实例吗？此操作不可撤销！')) return;

    try {
      // TODO: 实现删除实例的API调用
      console.log('删除实例:', instanceId);
      // await workflowApi.deleteInstance(instanceId);
      // loadInstances(); // 重新加载列表
    } catch (error) {
      console.error('删除实例失败:', error);
    }
  };

  const filteredInstances = instances.filter(instance => {
    const matchesSearch = instance.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         instance.target.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || instance.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between animate-fade-in">
        <div className="space-y-2">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Play className="h-10 w-10 text-primary animate-pulse-glow" />
              <div className="absolute inset-0 h-10 w-10 text-primary/20 animate-ping"></div>
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent">
                扫描实例管理
              </h1>
              <p className="text-muted-foreground font-mono">
                Scan Instance Management Console
              </p>
            </div>
          </div>
          <p className="text-muted-foreground">
            实时监控和管理所有安全扫描任务的执行状态
          </p>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={loadInstances}
            disabled={loading}
            className="cyber-button inline-flex items-center px-6 py-3 border border-border rounded-xl text-sm font-medium text-foreground bg-card hover:bg-accent transition-all disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </button>
          <Link
            to="/create"
            className="cyber-button inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-xl text-sm font-medium hover:bg-primary/90 transition-all shadow-lg"
          >
            <Plus className="h-5 w-5 mr-2" />
            创建扫描
          </Link>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="cyber-card p-6 animate-slide-in-right">
        <div className="flex flex-col sm:flex-row gap-6">
          <div className="flex-1">
            <label className="block text-sm font-medium text-muted-foreground mb-2">
              搜索扫描实例
            </label>
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <input
                type="text"
                placeholder="输入实例名称、目标地址或ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-12 pr-4 py-3 bg-background border border-border rounded-xl text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all"
              />
            </div>
          </div>
          <div className="sm:w-56">
            <label className="block text-sm font-medium text-muted-foreground mb-2">
              状态筛选
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full px-4 py-3 bg-background border border-border rounded-xl text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all"
            >
              <option value="all">🔍 所有状态</option>
              <option value="created">⚡ 已创建</option>
              <option value="running">🔄 运行中</option>
              <option value="completed">✅ 已完成</option>
              <option value="failed">❌ 失败</option>
              <option value="cancelled">⏸️ 已取消</option>
            </select>
          </div>
        </div>
      </div>

      {/* 实例列表 */}
      <div className="cyber-card">
        {error ? (
          <div className="text-center py-12">
            <XCircle className="h-16 w-16 text-red-400 mx-auto mb-6 animate-pulse" />
            <h3 className="text-xl font-semibold text-foreground mb-3">加载失败</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">{error}</p>
            <button
              onClick={loadInstances}
              className="cyber-button inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-xl font-medium hover:bg-primary/90 transition-all"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重新加载
            </button>
          </div>
        ) : loading ? (
          <div className="text-center py-12">
            <div className="relative">
              <RefreshCw className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
              <div className="absolute inset-0 h-12 w-12 text-primary/20 animate-ping mx-auto"></div>
            </div>
            <p className="text-muted-foreground text-lg">正在加载扫描实例...</p>
            <p className="text-muted-foreground/60 text-sm mt-2 font-mono">Loading scan instances...</p>
          </div>
        ) : filteredInstances.length === 0 ? (
          <div className="text-center py-16">
            <div className="relative mb-6">
              <Play className="h-20 w-20 text-muted-foreground/40 mx-auto" />
              <div className="absolute inset-0 h-20 w-20 text-primary/20 animate-pulse mx-auto"></div>
            </div>
            <h3 className="text-2xl font-semibold text-foreground mb-3">
              {searchTerm || statusFilter !== 'all' ? '🔍 没有找到匹配的实例' : '🚀 准备开始扫描'}
            </h3>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              {searchTerm || statusFilter !== 'all' ? '尝试调整搜索条件或筛选器' : '创建您的第一个安全扫描任务，开始网络安全检测'}
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Link
                to="/create"
                className="cyber-button inline-flex items-center px-8 py-4 bg-primary text-primary-foreground rounded-xl font-medium hover:bg-primary/90 transition-all shadow-lg text-lg"
              >
                <Play className="h-5 w-5 mr-3" />
                创建扫描任务
              </Link>
            )}
          </div>
        ) : (
          <div className="cyber-card overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gradient-to-r from-card to-card/80 border-b border-border">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      🎯 扫描实例
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      🌐 目标地址
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      📊 状态
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      ⚡ 进度
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      🕒 创建时间
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      ⏱️ 执行时长
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      🔧 操作
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                {filteredInstances.map((instance, index) => (
                  <tr
                    key={instance.instance_id}
                    className="hover:bg-accent/50 transition-colors animate-fade-in"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="text-sm font-semibold text-foreground">
                          {instance.name}
                        </div>
                        <div className="text-xs text-muted-foreground font-mono bg-muted/30 px-2 py-1 rounded">
                          {instance.workflow_id}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          ID: {instance.instance_id.slice(0, 8)}...
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                        <span className="text-sm font-mono text-foreground bg-primary/10 px-2 py-1 rounded">
                          {instance.target}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusIndicator
                        status={instance.status}
                        size="md"
                        animated={true}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="w-full max-w-xs">
                        <ProgressBar
                          progress={instance.progress || 0}
                          status={instance.status}
                          showPercentage={true}
                          showSteps={typeof instance.progress === 'object'}
                          size="md"
                          variant="cyber"
                          animated={instance.status === 'running'}
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-muted-foreground font-mono">
                        {formatDate(instance.created_at)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-muted-foreground font-mono">
                        {getDuration(instance.started_at, instance.completed_at)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        {/* 查看详情 */}
                        <Link
                          to={`/instances/${instance.instance_id}`}
                          className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-primary/10 text-primary border border-primary/20 rounded-lg hover:bg-primary/20 transition-all"
                          title="查看详情"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          详情
                        </Link>

                        {/* 停止按钮 (仅运行中的实例) */}
                        {instance.status === 'running' && (
                          <button
                            onClick={() => handleStopInstance(instance.instance_id)}
                            className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-yellow-500/10 text-yellow-400 border border-yellow-500/20 rounded-lg hover:bg-yellow-500/20 transition-all"
                            title="停止扫描"
                          >
                            <Pause className="h-3 w-3 mr-1" />
                            停止
                          </button>
                        )}

                        {/* 重新运行按钮 (失败或完成的实例) */}
                        {(instance.status === 'failed' || instance.status === 'completed') && (
                          <button
                            onClick={() => handleRestartInstance(instance.instance_id)}
                            className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-green-500/10 text-green-400 border border-green-500/20 rounded-lg hover:bg-green-500/20 transition-all"
                            title="重新运行"
                          >
                            <RotateCcw className="h-3 w-3 mr-1" />
                            重运行
                          </button>
                        )}

                        {/* 删除按钮 */}
                        <button
                          onClick={() => handleDeleteInstance(instance.instance_id)}
                          className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-red-500/10 text-red-400 border border-red-500/20 rounded-lg hover:bg-red-500/20 transition-all"
                          title="删除实例"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkflowInstances;
