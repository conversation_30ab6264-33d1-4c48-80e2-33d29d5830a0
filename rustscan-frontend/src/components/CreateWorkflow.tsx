import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { 
  Play, 
  FileText, 
  Target, 
  Settings, 
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { workflowApi, WorkflowDefinition, CreateWorkflowInstanceRequest } from '../services/api';

const CreateWorkflow: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const templateId = searchParams.get('template');

  const [templates, setTemplates] = useState<WorkflowDefinition[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [formData, setFormData] = useState({
    name: '',
    target: '',
    context: {}
  });
  const [loading, setLoading] = useState(false);
  const [templatesLoading, setTemplatesLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    if (templateId && templates.length > 0) {
      setSelectedTemplate(templateId);
      const template = templates.find(t => t.id === templateId);
      if (template) {
        setFormData(prev => ({
          ...prev,
          name: `${template.name} - ${new Date().toLocaleString()}`
        }));
      }
    }
  }, [templateId, templates]);

  const loadTemplates = async () => {
    try {
      setTemplatesLoading(true);
      const response = await workflowApi.getWorkflowTemplates();
      if (response.success && response.data) {
        setTemplates(response.data.templates);
      } else {
        setError(response.error?.message || '加载模板失败');
      }
    } catch (err) {
      setError('网络错误，请检查连接');
    } finally {
      setTemplatesLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTemplate) {
      setError('请选择一个工作流模板');
      return;
    }

    if (!formData.name.trim()) {
      setError('请输入工作流名称');
      return;
    }

    if (!formData.target.trim()) {
      setError('请输入扫描目标');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const request: CreateWorkflowInstanceRequest = {
        workflow_id: selectedTemplate,
        name: formData.name.trim(),
        target: formData.target.trim(),
        context: formData.context
      };

      const response = await workflowApi.createWorkflowInstance(request);
      
      if (response.success && response.data) {
        setSuccess('工作流实例创建成功！');
        setTimeout(() => {
          navigate(`/instances/${response.data!.instance_id}`);
        }, 1500);
      } else {
        setError(response.error?.message || '创建工作流实例失败');
      }
    } catch (err) {
      setError('网络错误，请检查连接');
    } finally {
      setLoading(false);
    }
  };

  const selectedTemplateData = templates.find(t => t.id === selectedTemplate);

  const validateTarget = (target: string) => {
    // 简单的目标验证
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/;
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    const urlRegex = /^https?:\/\/.+/;
    
    return ipRegex.test(target) || domainRegex.test(target) || urlRegex.test(target);
  };

  if (templatesLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
        <span className="ml-2 text-gray-600">加载模板...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8 animate-fade-in">
      {/* 页面标题 */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="relative">
            <Target className="h-12 w-12 text-primary animate-pulse-glow" />
            <div className="absolute inset-0 h-12 w-12 text-primary/20 animate-ping"></div>
          </div>
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent">
              启动安全扫描
            </h1>
            <p className="text-muted-foreground text-lg font-mono mt-2">
              Configure & Deploy Security Scan
            </p>
          </div>
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          选择扫描模板，配置目标参数，启动专业级网络安全扫描任务
        </p>
      </div>

      {/* 成功/错误消息 */}
      {success && (
        <div className="flex items-center p-4 bg-success-50 border border-success-200 rounded-lg">
          <CheckCircle className="h-5 w-5 text-success-500 mr-2" />
          <span className="text-success-700">{success}</span>
        </div>
      )}

      {error && (
        <div className="flex items-center p-4 bg-danger-50 border border-danger-200 rounded-lg">
          <AlertCircle className="h-5 w-5 text-danger-500 mr-2" />
          <span className="text-danger-700">{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 选择模板 */}
        <div className="card">
          <div className="flex items-center mb-4">
            <FileText className="h-5 w-5 text-primary-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">选择工作流模板</h3>
          </div>
          
          <div className="space-y-3">
            {templates.map((template) => (
              <label key={template.id} className="flex items-start p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="template"
                  value={template.id}
                  checked={selectedTemplate === template.id}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="mt-1 mr-3"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{template.name}</h4>
                    <span className="badge-info">{template.steps.length} 步骤</span>
                  </div>
                  <p className="text-sm text-gray-600">{template.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* 基本配置 */}
        <div className="card">
          <div className="flex items-center mb-4">
            <Settings className="h-5 w-5 text-primary-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">基本配置</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                工作流名称 *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="input"
                placeholder="输入工作流实例名称"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                扫描目标 *
              </label>
              <input
                type="text"
                value={formData.target}
                onChange={(e) => setFormData(prev => ({ ...prev, target: e.target.value }))}
                className={`input ${formData.target && !validateTarget(formData.target) ? 'border-danger-300' : ''}`}
                placeholder="例如: example.com, ***********, ***********/24"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                支持域名、IP地址、IP段或URL格式
              </p>
              {formData.target && !validateTarget(formData.target) && (
                <p className="text-xs text-danger-600 mt-1">
                  目标格式可能不正确，请检查
                </p>
              )}
            </div>
          </div>
        </div>

        {/* 模板详情 */}
        {selectedTemplateData && (
          <div className="card">
            <div className="flex items-center mb-4">
              <Target className="h-5 w-5 text-primary-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">执行计划</h3>
            </div>
            
            <div className="space-y-3">
              {selectedTemplateData.steps.map((step, index) => (
                <div key={step.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mr-3">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900">{step.name}</h4>
                      <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded">
                        {step.tool}
                      </span>
                    </div>
                    {step.depends_on.length > 0 && (
                      <p className="text-xs text-gray-500 mt-1">
                        依赖: {step.depends_on.join(', ')}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {selectedTemplateData.global_config && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">全局配置</h4>
                <div className="text-xs text-gray-600">
                  {Object.entries(selectedTemplateData.global_config).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span>{key}:</span>
                      <span>{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 提交按钮 */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/templates')}
            className="btn-secondary"
            disabled={loading}
          >
            取消
          </button>
          <button
            type="submit"
            className="btn-primary"
            disabled={loading || !selectedTemplate || !formData.name.trim() || !formData.target.trim()}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                创建中...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                创建并启动
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateWorkflow;
