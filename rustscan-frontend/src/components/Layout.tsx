import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  FileText,
  Play,
  Plus,
  Menu,
  X,
  Shield,
  Activity,
  Zap,
  Settings,
  Bell,
  Search,
  User,
  ChevronDown
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    {
      name: '概览',
      href: '/',
      icon: Home,
      description: '系统状态和统计信息'
    },
    {
      name: '工作流模板',
      href: '/templates',
      icon: FileText,
      description: '预定义的扫描模板'
    },
    {
      name: '扫描实例',
      href: '/instances',
      icon: Play,
      description: '正在运行和历史扫描'
    },
    {
      name: '创建扫描',
      href: '/create',
      icon: Zap,
      description: '启动新的安全扫描'
    },
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const getPageTitle = () => {
    const currentNav = navigation.find(nav => isActive(nav.href));
    return currentNav?.name || 'RustScan';
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 移动端侧边栏背景 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <aside className={`
        fixed inset-y-0 left-0 z-50 w-72 bg-card border-r border-border transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* 品牌区域 */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Shield className="h-8 w-8 text-primary" />
              <div className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full animate-pulse-glow"></div>
            </div>
            <div>
              <h1 className="text-lg font-bold text-foreground">RustScan</h1>
              <p className="text-xs text-muted-foreground">安全扫描平台</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`
                  group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-accent
                  ${active
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                  }
                `}
                onClick={() => setSidebarOpen(false)}
              >
                <Icon className={`
                  mr-3 h-5 w-5 flex-shrink-0
                  ${active ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'}
                `} />
                <div className="flex-1">
                  <div className={active ? 'text-primary-foreground' : 'text-foreground'}>{item.name}</div>
                  <div className={`text-xs ${active ? 'text-primary-foreground/80' : 'text-muted-foreground'}`}>
                    {item.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>

        {/* 状态指示器 */}
        <div className="p-4 border-t border-border">
          <div className="bg-accent rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-foreground">系统状态</span>
              </div>
              <span className="badge-success">在线</span>
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              所有服务正常运行
            </div>
          </div>
        </div>
      </aside>

      {/* 主内容区域 */}
      <div className="lg:pl-72">
        {/* 顶部导航栏 */}
        <header className="sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
          <div className="flex items-center justify-between h-16 px-6">
            {/* 移动端菜单按钮 */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"
              >
                <Menu className="h-5 w-5" />
              </button>

              {/* 页面标题 */}
              <div>
                <h1 className="text-xl font-semibold text-foreground">
                  {getPageTitle()}
                </h1>
                <p className="text-sm text-muted-foreground hidden sm:block">
                  现代化的网络安全扫描平台
                </p>
              </div>
            </div>

            {/* 右侧操作区 */}
            <div className="flex items-center space-x-4">
              {/* 搜索按钮 */}
              <button className="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent">
                <Search className="h-5 w-5" />
              </button>

              {/* 通知按钮 */}
              <button className="relative p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent">
                <Bell className="h-5 w-5" />
                <div className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"></div>
              </button>

              {/* 用户菜单 */}
              <div className="relative">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center space-x-2 p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"
                >
                  <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-primary-foreground" />
                  </div>
                  <ChevronDown className="h-4 w-4" />
                </button>

                {/* 用户下拉菜单 */}
                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg py-1 z-50">
                    <div className="px-3 py-2 border-b border-border">
                      <p className="text-sm font-medium text-foreground">管理员</p>
                      <p className="text-xs text-muted-foreground"><EMAIL></p>
                    </div>
                    <button className="flex items-center w-full px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-accent">
                      <Settings className="h-4 w-4 mr-2" />
                      设置
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="flex-1 p-6 animate-fade-in">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
