import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  FileText,
  Play,
  Menu,
  X,
  Shield,
  Zap
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    {
      name: '概览',
      href: '/',
      icon: Home,
      description: '系统状态和统计信息'
    },
    {
      name: '工作流模板',
      href: '/templates',
      icon: FileText,
      description: '预定义的扫描模板'
    },
    {
      name: '扫描实例',
      href: '/instances',
      icon: Play,
      description: '正在运行和历史扫描'
    },
    {
      name: '创建扫描',
      href: '/create',
      icon: Zap,
      description: '启动新的安全扫描'
    },
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const getPageTitle = () => {
    const currentNav = navigation.find(nav => isActive(nav.href));
    return currentNav?.name || 'RustScan';
  };

  return (
    <div className="min-h-screen bg-background flex">
      {/* 移动端侧边栏背景 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <aside className={`
        fixed inset-y-0 left-0 z-50 w-72 bg-card border-r border-border transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* 品牌区域 */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-border bg-gradient-to-r from-primary/5 to-cyan-500/5">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Shield className="h-10 w-10 text-primary animate-pulse-glow" />
              <div className="absolute inset-0 h-10 w-10 text-primary/20 animate-ping"></div>
              <div className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent animate-cyber-glow">
                CyberScan
              </h1>
              <p className="text-xs text-muted-foreground font-mono">
                v2.1.0 | Security Platform
              </p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`
                  group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-accent
                  ${active
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                  }
                `}
                onClick={() => setSidebarOpen(false)}
              >
                <Icon className={`
                  mr-3 h-5 w-5 flex-shrink-0
                  ${active ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'}
                `} />
                <div className="flex-1">
                  <div className={active ? 'text-primary-foreground' : 'text-foreground'}>{item.name}</div>
                  <div className={`text-xs ${active ? 'text-primary-foreground/80' : 'text-muted-foreground'}`}>
                    {item.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>

        {/* 状态指示器 */}
        <div className="p-4 border-t border-border">
          <div className="bg-accent rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-foreground">系统状态</span>
              </div>
              <span className="badge-success">在线</span>
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              所有服务正常运行
            </div>
          </div>
        </div>
      </aside>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col lg:ml-0">
        {/* 顶部导航栏 */}
        <header className="sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
          <div className="flex items-center justify-between h-16 px-6">
            {/* 移动端菜单按钮 */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"
              >
                <Menu className="h-5 w-5" />
              </button>

              {/* 页面标题 */}
              <div>
                <h1 className="text-xl font-semibold text-foreground">
                  {getPageTitle()}
                </h1>
                <p className="text-sm text-muted-foreground hidden sm:block">
                  现代化的网络安全扫描平台
                </p>
              </div>
            </div>

            {/* 右侧状态指示 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-muted-foreground">系统在线</span>
              </div>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="flex-1 p-6 animate-fade-in">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
