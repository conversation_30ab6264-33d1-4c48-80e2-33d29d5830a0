import React from 'react';

interface ProgressBarProps {
  progress: number | { completed_steps: number; percentage: number; total_steps: number };
  status?: string;
  showPercentage?: boolean;
  showSteps?: boolean;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  variant?: 'default' | 'cyber' | 'terminal';
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  status = 'running',
  showPercentage = true,
  showSteps = false,
  size = 'md',
  animated = true,
  variant = 'cyber'
}) => {
  const getProgressData = () => {
    if (typeof progress === 'number') {
      return {
        percentage: progress,
        completed_steps: 0,
        total_steps: 0
      };
    }
    return progress;
  };

  const progressData = getProgressData();
  const percentage = Math.min(100, Math.max(0, progressData.percentage));

  const getSizeClasses = () => {
    const sizes = {
      'sm': 'h-1.5',
      'md': 'h-2.5',
      'lg': 'h-4'
    };
    return sizes[size] || sizes['md'];
  };

  const getVariantClasses = () => {
    const variants = {
      'default': {
        container: 'bg-secondary rounded-full',
        bar: 'bg-primary rounded-full transition-all duration-500 ease-out',
        glow: ''
      },
      'cyber': {
        container: 'bg-secondary/50 rounded-full border border-border/30',
        bar: 'bg-gradient-to-r from-primary to-cyan-400 rounded-full transition-all duration-500 ease-out relative overflow-hidden',
        glow: 'shadow-lg shadow-primary/20'
      },
      'terminal': {
        container: 'bg-black/50 rounded border border-green-500/30',
        bar: 'bg-gradient-to-r from-green-400 to-green-500 transition-all duration-500 ease-out relative overflow-hidden',
        glow: 'shadow-lg shadow-green-500/30'
      }
    };
    return variants[variant] || variants['cyber'];
  };

  const getStatusColor = () => {
    const colors: Record<string, string> = {
      'completed': 'from-green-400 to-green-500',
      'running': 'from-primary to-cyan-400',
      'failed': 'from-red-400 to-red-500',
      'pending': 'from-yellow-400 to-yellow-500',
      'paused': 'from-gray-400 to-gray-500'
    };
    return colors[status] || colors['running'];
  };

  const variantClasses = getVariantClasses();
  const sizeClass = getSizeClasses();
  const statusColor = getStatusColor();

  return (
    <div className="space-y-2">
      {/* 进度信息 */}
      {(showPercentage || showSteps) && (
        <div className="flex items-center justify-between text-sm">
          {showSteps && progressData.total_steps > 0 && (
            <span className="text-muted-foreground font-mono">
              步骤 {progressData.completed_steps}/{progressData.total_steps}
            </span>
          )}
          {showPercentage && (
            <span className="text-foreground font-medium">
              {percentage.toFixed(1)}%
            </span>
          )}
        </div>
      )}

      {/* 进度条 */}
      <div className={`
        relative overflow-hidden
        ${variantClasses.container}
        ${sizeClass}
        ${variantClasses.glow}
      `}>
        {/* 进度填充 */}
        <div
          className={`
            h-full transition-all duration-700 ease-out
            ${variant === 'cyber' || variant === 'terminal' ? `bg-gradient-to-r ${statusColor}` : variantClasses.bar}
          `}
          style={{ width: `${percentage}%` }}
        >
          {/* 扫描线动画 */}
          {animated && variant === 'cyber' && status === 'running' && (
            <div className="absolute inset-0 overflow-hidden">
              <div className="scan-progress absolute inset-0">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-scan-line"></div>
              </div>
            </div>
          )}

          {/* 终端风格动画 */}
          {animated && variant === 'terminal' && status === 'running' && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-green-300/20 to-transparent animate-pulse"></div>
          )}
        </div>

        {/* 背景网格效果 (仅cyber变体) */}
        {variant === 'cyber' && (
          <div className="absolute inset-0 opacity-10">
            <div className="h-full w-full bg-gradient-to-r from-transparent via-primary/10 to-transparent"></div>
          </div>
        )}
      </div>

      {/* 状态文本 */}
      {status === 'running' && animated && (
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse"></div>
          <span className="font-mono">扫描进行中...</span>
        </div>
      )}
    </div>
  );
};

export default ProgressBar;
