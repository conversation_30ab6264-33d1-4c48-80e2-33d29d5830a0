import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Activity,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  Shield,
  Zap,
  ArrowUpRight,
  BarChart3,
  Target,
  AlertTriangle,
  Cpu,
  Database,
  Globe,
  Timer
} from 'lucide-react';
import { workflowApi } from '../services/api';

interface Statistics {
  templates: {
    total: number;
    available: string[];
  };
  instances: {
    total: number;
    running: number;
    completed: number;
    failed: number;
    pending: number;
  };
  tools: {
    available: string[];
    total_executions: number;
    success_rate: number;
  };
}

const Dashboard: React.FC = () => {
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStatistics();
  }, []);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const response = await workflowApi.getWorkflowStatistics();
      if (response.success) {
        setStatistics(response.data);
      } else {
        setError(response.error?.message || '加载统计数据失败');
      }
    } catch (err) {
      setError('网络错误，请检查连接');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-8">
        {/* 加载状态 */}
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded-lg w-1/4 mb-2"></div>
          <div className="h-4 bg-muted rounded-lg w-1/2 mb-8"></div>

          {/* 统计卡片骨架 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card">
                <div className="card-content">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-muted rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-muted rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 图表区域骨架 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <div className="card-content">
                <div className="h-6 bg-muted rounded w-1/3 mb-4"></div>
                <div className="space-y-3">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-4 bg-muted rounded"></div>
                  ))}
                </div>
              </div>
            </div>
            <div className="card">
              <div className="card-content">
                <div className="h-6 bg-muted rounded w-1/3 mb-4"></div>
                <div className="h-32 bg-muted rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-destructive/10 mb-4">
            <AlertTriangle className="h-8 w-8 text-destructive" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">加载失败</h3>
          <p className="text-muted-foreground mb-6 max-w-sm">{error}</p>
          <button onClick={loadStatistics} className="btn-primary">
            <ArrowUpRight className="h-4 w-4 mr-2" />
            重新加载
          </button>
        </div>
      </div>
    );
  }

  const stats = statistics!;

  return (
    <div className="space-y-8">
      {/* 网络安全控制台标题区域 */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-card via-card/95 to-card/90 border border-border/50 p-8 backdrop-blur-sm">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-cyan-500/5"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Shield className="h-12 w-12 text-primary animate-pulse-glow" />
                  <div className="absolute inset-0 h-12 w-12 text-primary/20 animate-ping"></div>
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent">
                    网络安全控制台
                  </h1>
                  <p className="text-muted-foreground text-lg font-mono">
                    CyberScan Security Platform v2.1.0
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400">系统在线</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-blue-400" />
                  <span className="text-muted-foreground">实时监控中</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Timer className="h-4 w-4 text-yellow-400" />
                  <span className="text-muted-foreground font-mono">
                    {new Date().toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <Link to="/create" className="cyber-button inline-flex items-center px-8 py-4 bg-primary text-primary-foreground font-medium rounded-xl hover:bg-primary/90 transition-all shadow-lg hover:shadow-xl">
                <Zap className="h-5 w-5 mr-3" />
                启动扫描任务
              </Link>
            </div>
          </div>
        </div>
        {/* 装饰性元素 */}
        <div className="absolute top-0 right-0 -mt-8 -mr-8 h-32 w-32 rounded-full bg-primary/5 animate-pulse"></div>
        <div className="absolute bottom-0 left-0 -mb-12 -ml-12 h-40 w-40 rounded-full bg-cyan-500/5 animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 right-1/4 h-2 w-2 bg-primary rounded-full animate-ping" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/4 right-1/3 h-1 w-1 bg-cyan-400 rounded-full animate-ping" style={{animationDelay: '3s'}}></div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 工作流模板 */}
        <div className="metric-card group">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-xl border border-blue-500/20 group-hover:border-blue-500/40 transition-all">
                <FileText className="h-7 w-7 text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">扫描模板</p>
                <p className="metric-value">{stats.templates.total}</p>
                <p className="text-xs text-muted-foreground mt-1">可用扫描配置</p>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <ArrowUpRight className="h-5 w-5 text-muted-foreground group-hover:text-blue-400 transition-colors" />
              <div className="text-xs text-green-400 font-mono mt-2">+{stats.templates.available.length}</div>
            </div>
          </div>
        </div>

        {/* 总扫描数 */}
        <div className="metric-card group">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 bg-gradient-to-br from-green-500/10 to-green-600/10 rounded-xl border border-green-500/20 group-hover:border-green-500/40 transition-all">
                <Database className="h-7 w-7 text-green-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">总扫描数</p>
                <p className="metric-value">{stats.instances.total}</p>
                <p className="text-xs text-muted-foreground mt-1">历史扫描任务</p>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <ArrowUpRight className="h-5 w-5 text-muted-foreground group-hover:text-green-400 transition-colors" />
              <div className="text-xs text-green-400 font-mono mt-2">TOTAL</div>
            </div>
          </div>
        </div>

        {/* 运行中 */}
        <div className="metric-card group">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 bg-gradient-to-br from-orange-500/10 to-orange-600/10 rounded-xl border border-orange-500/20 group-hover:border-orange-500/40 transition-all">
                <Activity className="h-7 w-7 text-orange-400 animate-pulse" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">运行中</p>
                <p className="metric-value">{stats.instances.running}</p>
                <p className="text-xs text-muted-foreground mt-1">活跃扫描任务</p>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <div className="h-3 w-3 bg-orange-500 rounded-full animate-pulse-glow"></div>
              <div className="text-xs text-orange-400 font-mono mt-2">LIVE</div>
            </div>
          </div>
        </div>

        {/* 成功率 */}
        <div className="metric-card group">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 bg-gradient-to-br from-purple-500/10 to-purple-600/10 rounded-xl border border-purple-500/20 group-hover:border-purple-500/40 transition-all">
                <TrendingUp className="h-7 w-7 text-purple-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">成功率</p>
                <p className="metric-value">
                  {(stats.tools.success_rate * 100).toFixed(1)}%
                </p>
                <p className="text-xs text-muted-foreground mt-1">扫描成功率</p>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <ArrowUpRight className="h-5 w-5 text-muted-foreground group-hover:text-purple-400 transition-colors" />
              <div className="text-xs text-purple-400 font-mono mt-2">
                {stats.tools.success_rate >= 0.9 ? 'HIGH' : stats.tools.success_rate >= 0.7 ? 'MED' : 'LOW'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 实例状态分布 */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              <h3 className="card-title text-lg">扫描状态</h3>
            </div>
            <p className="card-description">实例执行状态分布</p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-foreground">已完成</span>
                </div>
                <span className="text-lg font-bold text-green-600">{stats.instances.completed}</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Activity className="h-5 w-5 text-orange-600" />
                  <span className="text-sm font-medium text-foreground">运行中</span>
                </div>
                <span className="text-lg font-bold text-orange-600">{stats.instances.running}</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <span className="text-sm font-medium text-foreground">失败</span>
                </div>
                <span className="text-lg font-bold text-red-600">{stats.instances.failed}</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-gray-600" />
                  <span className="text-sm font-medium text-foreground">等待中</span>
                </div>
                <span className="text-lg font-bold text-gray-600">{stats.instances.pending}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 可用工具 */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-primary" />
              <h3 className="card-title text-lg">扫描工具</h3>
            </div>
            <p className="card-description">可用的安全扫描工具</p>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 gap-3">
              {stats.tools.available.map((tool) => (
                <div key={tool} className="flex items-center justify-between p-3 bg-accent rounded-lg hover:bg-accent/80 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Cpu className="h-4 w-4 text-primary" />
                    </div>
                    <span className="text-sm font-medium text-foreground capitalize">{tool}</span>
                  </div>
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-border">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">总执行次数</span>
                <span className="text-sm font-bold text-foreground">{stats.tools.total_executions}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 系统性能 */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Cpu className="h-5 w-5 text-primary" />
              <h3 className="card-title text-lg">系统状态</h3>
            </div>
            <p className="card-description">实时系统监控</p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Globe className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-muted-foreground">API 状态</span>
                </div>
                <span className="badge-success">在线</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-muted-foreground">数据库</span>
                </div>
                <span className="badge-success">正常</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Timer className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-muted-foreground">平均响应</span>
                </div>
                <span className="text-sm font-medium text-foreground">45ms</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-purple-500" />
                  <span className="text-sm text-muted-foreground">活跃连接</span>
                </div>
                <span className="text-sm font-medium text-foreground">12</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="card-title text-xl">快速操作</h3>
              <p className="card-description">常用功能快速入口</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-muted-foreground">系统就绪</span>
            </div>
          </div>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link
              to="/templates"
              className="group relative overflow-hidden rounded-lg border border-border p-6 hover:shadow-lg transition-all duration-200 hover:border-primary/50"
            >
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-blue-100 rounded-xl group-hover:bg-blue-200 transition-colors">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                    浏览模板
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    查看预定义的扫描模板
                  </p>
                </div>
                <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </Link>

            <Link
              to="/create"
              className="group relative overflow-hidden rounded-lg border border-border p-6 hover:shadow-lg transition-all duration-200 hover:border-primary/50"
            >
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-green-100 rounded-xl group-hover:bg-green-200 transition-colors">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                    开始扫描
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    创建新的安全扫描任务
                  </p>
                </div>
                <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </Link>

            <Link
              to="/instances"
              className="group relative overflow-hidden rounded-lg border border-border p-6 hover:shadow-lg transition-all duration-200 hover:border-primary/50"
            >
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-orange-100 rounded-xl group-hover:bg-orange-200 transition-colors">
                  <Activity className="h-6 w-6 text-orange-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                    监控扫描
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    查看运行中的扫描任务
                  </p>
                </div>
                <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
