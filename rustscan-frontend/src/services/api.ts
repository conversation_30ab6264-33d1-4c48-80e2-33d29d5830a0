import axios from 'axios';

// API 基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 类型定义
export interface WorkflowStep {
  id: string;
  name: string;
  tool: string;
  parameters: Record<string, any>;
  depends_on: string[];
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  version: string;
  steps: WorkflowStep[];
  global_config?: Record<string, any>;
}

export interface WorkflowProgress {
  completed_steps: number;
  percentage: number;
  total_steps: number;
}

export interface WorkflowInstance {
  instance_id: string;
  workflow_id: string;
  name: string;
  target: string;
  status: string;
  current_step?: string;
  progress?: number | WorkflowProgress;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface WorkflowStepInstance {
  id: string;
  name: string;
  status: string;
  tool: string;
  started_at?: string;
  completed_at?: string;
  execution_time?: number;
}

export interface WorkflowStatus {
  instance_id: string;
  status: string;
  current_step?: string;
  progress: {
    completed_steps: number;
    total_steps: number;
    percentage: number;
  };
  steps: WorkflowStepInstance[];
  created_at: string;
  started_at?: string;
}

export interface CreateWorkflowInstanceRequest {
  workflow_id: string;
  name: string;
  target: string;
  context?: Record<string, any>;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
}

// API 方法
export const workflowApi = {
  // 获取健康状态
  getHealth: async () => {
    const response = await api.get('/health');
    return response.data;
  },

  // 获取工作流模板
  getWorkflowTemplates: async (): Promise<ApiResponse<{ templates: WorkflowDefinition[]; count: number }>> => {
    const response = await api.get('/workflow-templates');
    return response.data;
  },

  // 获取工作流实例列表
  getWorkflowInstances: async (): Promise<ApiResponse<{ instances: WorkflowInstance[]; count: number }>> => {
    const response = await api.get('/workflow-instances');
    return response.data;
  },

  // 创建工作流实例
  createWorkflowInstance: async (request: CreateWorkflowInstanceRequest): Promise<ApiResponse<WorkflowInstance>> => {
    const response = await api.post('/workflow-instances', request);
    return response.data;
  },

  // 获取工作流状态
  getWorkflowStatus: async (instanceId: string): Promise<ApiResponse<WorkflowStatus>> => {
    const response = await api.get(`/workflow-instances/${instanceId}/status`);
    return response.data;
  },

  // 获取工作流统计
  getWorkflowStatistics: async () => {
    const response = await api.get('/workflow-statistics');
    return response.data;
  },
};

export default api;
