import React from 'react';

export interface WebSocketMessage {
  type: string;
  data?: any;
  message?: string;
  timestamp?: string;
}

export interface WorkflowStatusUpdate {
  type: 'workflow_status_update';
  data: {
    instance_id?: string;
    status?: string;
    progress?: any;
    timestamp: string;
    active_instances?: number;
    completed_today?: number;
  };
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private isConnecting = false;

  constructor() {
    // 根据当前协议选择 WebSocket 协议
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    const port = process.env.NODE_ENV === 'development' ? '8080' : window.location.port;
    this.url = `${protocol}//${host}:${port}/ws`;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Already connecting'));
        return;
      }

      this.isConnecting = true;

      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // 发送订阅消息
          this.send({
            type: 'subscribe'
          });

          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.ws = null;

          // 自动重连
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
              this.connect().catch(console.error);
            }, this.reconnectDelay * this.reconnectAttempts);
          } else {
            console.error('Max reconnection attempts reached');
            this.emit('connection_failed', { message: 'Failed to reconnect to server' });
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.reconnectAttempts = this.maxReconnectAttempts; // 防止自动重连
  }

  send(message: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  // 事件监听器管理
  on(eventType: string, handler: WebSocketEventHandler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  off(eventType: string, handler: WebSocketEventHandler) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(eventType: string, data: any) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler({ type: eventType, data });
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
    }
  }

  private handleMessage(message: WebSocketMessage) {
    console.log('WebSocket message received:', message);

    // 发送到特定类型的监听器
    this.emit(message.type, message.data);

    // 发送到通用监听器
    this.emit('message', message);

    // 处理特定消息类型
    switch (message.type) {
      case 'workflow_status_update':
        this.emit('workflow_update', message.data);
        break;
      case 'subscription_confirmed':
        console.log('Subscription confirmed:', message.message);
        break;
      case 'pong':
        console.log('Pong received');
        break;
      case 'error':
        console.error('WebSocket error message:', message.message);
        break;
    }
  }

  // 发送心跳
  ping() {
    this.send({
      type: 'ping',
      timestamp: new Date().toISOString()
    });
  }

  // 获取连接状态
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }
}

// 创建单例实例
export const websocketService = new WebSocketService();

// React Hook for WebSocket
export const useWebSocket = () => {
  const [isConnected, setIsConnected] = React.useState(websocketService.isConnected);
  const [connectionState, setConnectionState] = React.useState(websocketService.connectionState);

  React.useEffect(() => {
    const updateConnectionState = () => {
      setIsConnected(websocketService.isConnected);
      setConnectionState(websocketService.connectionState);
    };

    // 监听连接状态变化
    const handleOpen = () => updateConnectionState();
    const handleClose = () => updateConnectionState();
    const handleError = () => updateConnectionState();

    websocketService.on('connection_opened', handleOpen);
    websocketService.on('connection_closed', handleClose);
    websocketService.on('connection_failed', handleError);

    // 尝试连接
    if (!websocketService.isConnected) {
      websocketService.connect().catch(console.error);
    }

    return () => {
      websocketService.off('connection_opened', handleOpen);
      websocketService.off('connection_closed', handleClose);
      websocketService.off('connection_failed', handleError);
    };
  }, []);

  return {
    isConnected,
    connectionState,
    send: websocketService.send.bind(websocketService),
    on: websocketService.on.bind(websocketService),
    off: websocketService.off.bind(websocketService),
  };
};

export default websocketService;
