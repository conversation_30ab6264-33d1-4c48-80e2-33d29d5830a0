<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="45" fill="#0f172a" stroke="url(#grad1)" stroke-width="3"/>
  
  <!-- Shield shape -->
  <path d="M50 15 L35 25 L35 45 Q35 60 50 75 Q65 60 65 45 L65 25 Z" fill="url(#grad1)" opacity="0.9"/>
  
  <!-- Inner shield -->
  <path d="M50 25 L40 30 L40 45 Q40 55 50 65 Q60 55 60 45 L60 30 Z" fill="#0f172a"/>
  
  <!-- Scan lines -->
  <line x1="42" y1="35" x2="58" y2="35" stroke="#22c55e" stroke-width="1.5" opacity="0.8"/>
  <line x1="42" y1="40" x2="58" y2="40" stroke="#22c55e" stroke-width="1.5" opacity="0.6"/>
  <line x1="42" y1="45" x2="58" y2="45" stroke="#22c55e" stroke-width="1.5" opacity="0.4"/>
  <line x1="42" y1="50" x2="58" y2="50" stroke="#22c55e" stroke-width="1.5" opacity="0.2"/>
</svg>
