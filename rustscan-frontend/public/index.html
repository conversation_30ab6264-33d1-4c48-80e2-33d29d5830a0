<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#22c55e" />
    <meta
      name="description"
      content="CyberScan Security Platform - 专业网络安全扫描平台"
    />
    <meta name="keywords" content="网络安全,扫描,渗透测试,安全检测,RustScan" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>CyberScan - 网络安全扫描平台</title>
  </head>
  <body>
    <noscript>您需要启用 JavaScript 来运行此应用程序。</noscript>
    <div id="root"></div>

    <script>
      // 处理浏览器扩展相关的错误，避免控制台噪音
      window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('Extension context invalidated')) {
          e.preventDefault();
          return false;
        }
      });

      // 处理未捕获的 Promise 拒绝
      window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.message && e.reason.message.includes('Extension context invalidated')) {
          e.preventDefault();
          return false;
        }
      });
    </script>
  </body>
</html>
