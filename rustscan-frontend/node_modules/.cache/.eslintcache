[{"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx": "1", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx": "2", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx": "3", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx": "4", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx": "5", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx": "6", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx": "7", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts": "8", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx": "9", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/StatusIndicator.tsx": "10", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/ProgressBar.tsx": "11"}, {"size": 274, "mtime": 1753019473238, "results": "12", "hashOfConfig": "13"}, {"size": 914, "mtime": 1753019202880, "results": "14", "hashOfConfig": "13"}, {"size": 11800, "mtime": 1753019465993, "results": "15", "hashOfConfig": "13"}, {"size": 22103, "mtime": 1753030228523, "results": "16", "hashOfConfig": "13"}, {"size": 6846, "mtime": 1753030124328, "results": "17", "hashOfConfig": "13"}, {"size": 12405, "mtime": 1753030737601, "results": "18", "hashOfConfig": "13"}, {"size": 10327, "mtime": 1753023037275, "results": "19", "hashOfConfig": "13"}, {"size": 3529, "mtime": 1753028789967, "results": "20", "hashOfConfig": "13"}, {"size": 19138, "mtime": 1753030698815, "results": "21", "hashOfConfig": "13"}, {"size": 3814, "mtime": 1753030523787, "results": "22", "hashOfConfig": "13"}, {"size": 4792, "mtime": 1753030534426, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ud84nm", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx", ["57", "58"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx", ["59", "60", "61"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/StatusIndicator.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/ProgressBar.tsx", [], [], {"ruleId": "62", "severity": 1, "message": "63", "line": 29, "column": 6, "nodeType": "64", "endLine": 29, "endColumn": 10, "suggestions": "65"}, {"ruleId": "62", "severity": 1, "message": "63", "line": 41, "column": 6, "nodeType": "64", "endLine": 41, "endColumn": 35, "suggestions": "66"}, {"ruleId": "67", "severity": 1, "message": "68", "line": 50, "column": 9, "nodeType": "69", "messageId": "70", "endLine": 50, "endColumn": 22}, {"ruleId": "67", "severity": 1, "message": "71", "line": 68, "column": 9, "nodeType": "69", "messageId": "70", "endLine": 68, "endColumn": 23}, {"ruleId": "67", "severity": 1, "message": "72", "line": 103, "column": 9, "nodeType": "69", "messageId": "70", "endLine": 103, "endColumn": 30}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadWorkflowStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["73"], ["74"], "@typescript-eslint/no-unused-vars", "'getStatusIcon' is assigned a value but never used.", "Identifier", "unusedVar", "'getStatusBadge' is assigned a value but never used.", "'getProgressPercentage' is assigned a value but never used.", {"desc": "75", "fix": "76"}, {"desc": "77", "fix": "78"}, "Update the dependencies array to be: [id, loadWorkflowStatus]", {"range": "79", "text": "80"}, "Update the dependencies array to be: [autoRefresh, loadWorkflowStatus, status?.status]", {"range": "81", "text": "82"}, [727, 731], "[id, loadWorkflowStatus]", [1020, 1049], "[autoRefresh, loadWorkflowStatus, status?.status]"]