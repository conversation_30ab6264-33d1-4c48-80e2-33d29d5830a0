[{"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx": "1", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx": "2", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx": "3", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx": "4", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx": "5", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx": "6", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx": "7", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx": "8", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts": "9"}, {"size": 274, "mtime": 1753019473238, "results": "10", "hashOfConfig": "11"}, {"size": 914, "mtime": 1753019202880, "results": "12", "hashOfConfig": "11"}, {"size": 11800, "mtime": 1753019465993, "results": "13", "hashOfConfig": "11"}, {"size": 11707, "mtime": 1753019423260, "results": "14", "hashOfConfig": "11"}, {"size": 19194, "mtime": 1753022980007, "results": "15", "hashOfConfig": "11"}, {"size": 6513, "mtime": 1753023058823, "results": "16", "hashOfConfig": "11"}, {"size": 10779, "mtime": 1753019367808, "results": "17", "hashOfConfig": "11"}, {"size": 10327, "mtime": 1753023037275, "results": "18", "hashOfConfig": "11"}, {"size": 3120, "mtime": 1753019190705, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ud84nm", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx", ["47", "48"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts", [], [], {"ruleId": "49", "severity": 1, "message": "50", "line": 29, "column": 6, "nodeType": "51", "endLine": 29, "endColumn": 10, "suggestions": "52"}, {"ruleId": "49", "severity": 1, "message": "50", "line": 41, "column": 6, "nodeType": "51", "endLine": 41, "endColumn": 35, "suggestions": "53"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadWorkflowStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["54"], ["55"], {"desc": "56", "fix": "57"}, {"desc": "58", "fix": "59"}, "Update the dependencies array to be: [id, loadWorkflowStatus]", {"range": "60", "text": "61"}, "Update the dependencies array to be: [autoRefresh, loadWorkflowStatus, status?.status]", {"range": "62", "text": "63"}, [727, 731], "[id, loadWorkflowStatus]", [1020, 1049], "[autoRefresh, loadWorkflowStatus, status?.status]"]