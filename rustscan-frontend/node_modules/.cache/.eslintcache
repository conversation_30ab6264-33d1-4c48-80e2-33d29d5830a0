[{"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx": "1", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx": "2", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx": "3", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx": "4", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx": "5", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx": "6", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx": "7", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts": "8", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx": "9", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/StatusIndicator.tsx": "10", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/ProgressBar.tsx": "11", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/NotificationSystem.tsx": "12", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/websocket.ts": "13"}, {"size": 274, "mtime": 1753019473238, "results": "14", "hashOfConfig": "15"}, {"size": 1063, "mtime": 1753031383894, "results": "16", "hashOfConfig": "15"}, {"size": 11800, "mtime": 1753019465993, "results": "17", "hashOfConfig": "15"}, {"size": 22103, "mtime": 1753030228523, "results": "18", "hashOfConfig": "15"}, {"size": 6846, "mtime": 1753030124328, "results": "19", "hashOfConfig": "15"}, {"size": 12405, "mtime": 1753030737601, "results": "20", "hashOfConfig": "15"}, {"size": 10327, "mtime": 1753023037275, "results": "21", "hashOfConfig": "15"}, {"size": 4352, "mtime": 1753031145556, "results": "22", "hashOfConfig": "15"}, {"size": 20610, "mtime": 1753031415614, "results": "23", "hashOfConfig": "15"}, {"size": 3814, "mtime": 1753030523787, "results": "24", "hashOfConfig": "15"}, {"size": 4792, "mtime": 1753030534426, "results": "25", "hashOfConfig": "15"}, {"size": 6070, "mtime": 1753031176272, "results": "26", "hashOfConfig": "15"}, {"size": 7142, "mtime": 1753031343658, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ud84nm", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx", ["67", "68"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx", ["69", "70", "71"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/StatusIndicator.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/ProgressBar.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/NotificationSystem.tsx", ["72"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/websocket.ts", [], [], {"ruleId": "73", "severity": 1, "message": "74", "line": 29, "column": 6, "nodeType": "75", "endLine": 29, "endColumn": 10, "suggestions": "76"}, {"ruleId": "73", "severity": 1, "message": "74", "line": 41, "column": 6, "nodeType": "75", "endLine": 41, "endColumn": 35, "suggestions": "77"}, {"ruleId": "78", "severity": 1, "message": "79", "line": 81, "column": 9, "nodeType": "80", "messageId": "81", "endLine": 81, "endColumn": 22}, {"ruleId": "78", "severity": 1, "message": "82", "line": 99, "column": 9, "nodeType": "80", "messageId": "81", "endLine": 99, "endColumn": 23}, {"ruleId": "78", "severity": 1, "message": "83", "line": 134, "column": 9, "nodeType": "80", "messageId": "81", "endLine": 134, "endColumn": 30}, {"ruleId": "73", "severity": 1, "message": "84", "line": 53, "column": 6, "nodeType": "75", "endLine": 53, "endColumn": 8, "suggestions": "85"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadWorkflowStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["86"], ["87"], "@typescript-eslint/no-unused-vars", "'getStatusIcon' is assigned a value but never used.", "Identifier", "unusedVar", "'getStatusBadge' is assigned a value but never used.", "'getProgressPercentage' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["88"], {"desc": "89", "fix": "90"}, {"desc": "91", "fix": "92"}, {"desc": "93", "fix": "94"}, "Update the dependencies array to be: [id, loadWorkflowStatus]", {"range": "95", "text": "96"}, "Update the dependencies array to be: [autoRefresh, loadWorkflowStatus, status?.status]", {"range": "97", "text": "98"}, "Update the dependencies array to be: [removeNotification]", {"range": "99", "text": "100"}, [727, 731], "[id, loadWorkflowStatus]", [1020, 1049], "[autoRefresh, loadWorkflowStatus, status?.status]", [1681, 1683], "[removeNotification]"]