[{"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx": "1", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx": "2", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx": "3", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx": "4", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx": "5", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx": "6", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx": "7", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts": "8"}, {"size": 274, "mtime": 1753019473238, "results": "9", "hashOfConfig": "10"}, {"size": 914, "mtime": 1753019202880, "results": "11", "hashOfConfig": "10"}, {"size": 11800, "mtime": 1753019465993, "results": "12", "hashOfConfig": "10"}, {"size": 19194, "mtime": 1753022980007, "results": "13", "hashOfConfig": "10"}, {"size": 6513, "mtime": 1753023058823, "results": "14", "hashOfConfig": "10"}, {"size": 10779, "mtime": 1753019367808, "results": "15", "hashOfConfig": "10"}, {"size": 10327, "mtime": 1753023037275, "results": "16", "hashOfConfig": "10"}, {"size": 3354, "mtime": 1753028140714, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ud84nm", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx", ["42", "43"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts", [], [], {"ruleId": "44", "severity": 1, "message": "45", "line": 29, "column": 6, "nodeType": "46", "endLine": 29, "endColumn": 10, "suggestions": "47"}, {"ruleId": "44", "severity": 1, "message": "45", "line": 41, "column": 6, "nodeType": "46", "endLine": 41, "endColumn": 35, "suggestions": "48"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadWorkflowStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["49"], ["50"], {"desc": "51", "fix": "52"}, {"desc": "53", "fix": "54"}, "Update the dependencies array to be: [id, loadWorkflowStatus]", {"range": "55", "text": "56"}, "Update the dependencies array to be: [autoRefresh, loadWorkflowStatus, status?.status]", {"range": "57", "text": "58"}, [727, 731], "[id, loadWorkflowStatus]", [1020, 1049], "[autoRefresh, loadWorkflowStatus, status?.status]"]