[{"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx": "1", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx": "2", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx": "3", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx": "4", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx": "5", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx": "6", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx": "7", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx": "8", "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts": "9"}, {"size": 274, "mtime": 1753019473238, "results": "10", "hashOfConfig": "11"}, {"size": 914, "mtime": 1753019202880, "results": "12", "hashOfConfig": "11"}, {"size": 11800, "mtime": 1753019465993, "results": "13", "hashOfConfig": "11"}, {"size": 11707, "mtime": 1753019423260, "results": "14", "hashOfConfig": "11"}, {"size": 19210, "mtime": 1753022759775, "results": "15", "hashOfConfig": "11"}, {"size": 8292, "mtime": 1753022585288, "results": "16", "hashOfConfig": "11"}, {"size": 10779, "mtime": 1753019367808, "results": "17", "hashOfConfig": "11"}, {"size": 10351, "mtime": 1753019323530, "results": "18", "hashOfConfig": "11"}, {"size": 3120, "mtime": 1753019190705, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ud84nm", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/index.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowDetail.tsx", ["47", "48"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowInstances.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Dashboard.tsx", ["49", "50"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx", ["51", "52"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx", [], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx", ["53", "54"], [], "/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts", [], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 29, "column": 6, "nodeType": "57", "endLine": 29, "endColumn": 10, "suggestions": "58"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 41, "column": 6, "nodeType": "57", "endLine": 41, "endColumn": 35, "suggestions": "59"}, {"ruleId": "60", "severity": 1, "message": "61", "line": 6, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 6, "endColumn": 7}, {"ruleId": "60", "severity": 1, "message": "64", "line": 13, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 13, "endColumn": 7}, {"ruleId": "60", "severity": 1, "message": "64", "line": 7, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 7, "endColumn": 7}, {"ruleId": "60", "severity": 1, "message": "65", "line": 11, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 11, "endColumn": 11}, {"ruleId": "60", "severity": 1, "message": "66", "line": 7, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 7, "endColumn": 8}, {"ruleId": "60", "severity": 1, "message": "67", "line": 8, "column": 3, "nodeType": "62", "messageId": "63", "endLine": 8, "endColumn": 8}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadWorkflowStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["68"], ["69"], "@typescript-eslint/no-unused-vars", "'Play' is defined but never used.", "Identifier", "unusedVar", "'Plus' is defined but never used.", "'Activity' is defined but never used.", "'Clock' is defined but never used.", "'Users' is defined but never used.", {"desc": "70", "fix": "71"}, {"desc": "72", "fix": "73"}, "Update the dependencies array to be: [id, loadWorkflowStatus]", {"range": "74", "text": "75"}, "Update the dependencies array to be: [autoRefresh, loadWorkflowStatus, status?.status]", {"range": "76", "text": "77"}, [727, 731], "[id, loadWorkflowStatus]", [1020, 1049], "[autoRefresh, loadWorkflowStatus, status?.status]"]