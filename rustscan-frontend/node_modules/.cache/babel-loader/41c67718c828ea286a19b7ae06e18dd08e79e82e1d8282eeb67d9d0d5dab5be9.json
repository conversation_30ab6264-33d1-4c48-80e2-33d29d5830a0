{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/StatusIndicator.tsx\";\nimport React from 'react';\nimport { CheckCircle, XCircle, Clock, Loader2, AlertTriangle, Pause, Play, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatusIndicator = ({\n  status,\n  size = 'md',\n  showText = true,\n  animated = true\n}) => {\n  const getStatusConfig = status => {\n    const configs = {\n      'completed': {\n        icon: CheckCircle,\n        text: '已完成',\n        className: 'status-completed',\n        iconColor: 'text-green-400',\n        bgColor: 'bg-green-500/10',\n        borderColor: 'border-green-500/20',\n        glowColor: 'shadow-green-500/20'\n      },\n      'running': {\n        icon: animated ? Loader2 : Play,\n        text: '运行中',\n        className: 'status-running',\n        iconColor: 'text-blue-400',\n        bgColor: 'bg-blue-500/10',\n        borderColor: 'border-blue-500/20',\n        glowColor: 'shadow-blue-500/20',\n        animate: animated ? 'animate-spin' : ''\n      },\n      'failed': {\n        icon: XCircle,\n        text: '失败',\n        className: 'status-failed',\n        iconColor: 'text-red-400',\n        bgColor: 'bg-red-500/10',\n        borderColor: 'border-red-500/20',\n        glowColor: 'shadow-red-500/20'\n      },\n      'pending': {\n        icon: Clock,\n        text: '等待中',\n        className: 'status-pending',\n        iconColor: 'text-yellow-400',\n        bgColor: 'bg-yellow-500/10',\n        borderColor: 'border-yellow-500/20',\n        glowColor: 'shadow-yellow-500/20'\n      },\n      'paused': {\n        icon: Pause,\n        text: '已暂停',\n        className: 'status-indicator',\n        iconColor: 'text-gray-400',\n        bgColor: 'bg-gray-500/10',\n        borderColor: 'border-gray-500/20',\n        glowColor: 'shadow-gray-500/20'\n      },\n      'created': {\n        icon: Zap,\n        text: '已创建',\n        className: 'status-indicator',\n        iconColor: 'text-cyan-400',\n        bgColor: 'bg-cyan-500/10',\n        borderColor: 'border-cyan-500/20',\n        glowColor: 'shadow-cyan-500/20'\n      },\n      'error': {\n        icon: AlertTriangle,\n        text: '错误',\n        className: 'status-failed',\n        iconColor: 'text-red-400',\n        bgColor: 'bg-red-500/10',\n        borderColor: 'border-red-500/20',\n        glowColor: 'shadow-red-500/20'\n      }\n    };\n    return configs[status.toLowerCase()] || configs['pending'];\n  };\n  const getSizeClasses = size => {\n    const sizes = {\n      'sm': {\n        container: 'px-2 py-1 text-xs',\n        icon: 'h-3 w-3',\n        text: 'text-xs'\n      },\n      'md': {\n        container: 'px-3 py-1.5 text-sm',\n        icon: 'h-4 w-4',\n        text: 'text-sm'\n      },\n      'lg': {\n        container: 'px-4 py-2 text-base',\n        icon: 'h-5 w-5',\n        text: 'text-base'\n      }\n    };\n    return sizes[size] || sizes['md'];\n  };\n  const config = getStatusConfig(status);\n  const sizeClasses = getSizeClasses(size);\n  const Icon = config.icon;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      inline-flex items-center gap-2 rounded-full font-medium transition-all duration-300\n      ${config.className}\n      ${config.bgColor}\n      ${config.borderColor}\n      ${sizeClasses.container}\n      border\n      ${animated ? 'hover:scale-105' : ''}\n      ${animated && config.glowColor ? `hover:shadow-lg ${config.glowColor}` : ''}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(Icon, {\n      className: `\n        ${sizeClasses.icon} \n        ${config.iconColor}\n        ${config.animate || ''}\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), showText && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `${sizeClasses.text} font-medium`,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_c = StatusIndicator;\nexport default StatusIndicator;\nvar _c;\n$RefreshReg$(_c, \"StatusIndicator\");", "map": {"version": 3, "names": ["React", "CheckCircle", "XCircle", "Clock", "Loader2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pause", "Play", "Zap", "jsxDEV", "_jsxDEV", "StatusIndicator", "status", "size", "showText", "animated", "getStatusConfig", "configs", "icon", "text", "className", "iconColor", "bgColor", "borderColor", "glowColor", "animate", "toLowerCase", "getSizeClasses", "sizes", "container", "config", "sizeClasses", "Icon", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/StatusIndicator.tsx"], "sourcesContent": ["import React from 'react';\nimport { \n  Check<PERSON>ircle, \n  XCircle, \n  Clock, \n  Loader2, \n  <PERSON><PERSON><PERSON><PERSON>gle,\n  Pause,\n  Play,\n  Zap\n} from 'lucide-react';\n\ninterface StatusIndicatorProps {\n  status: string;\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n  animated?: boolean;\n}\n\nconst StatusIndicator: React.FC<StatusIndicatorProps> = ({ \n  status, \n  size = 'md', \n  showText = true,\n  animated = true \n}) => {\n  const getStatusConfig = (status: string) => {\n    const configs: Record<string, any> = {\n      'completed': {\n        icon: CheckCircle,\n        text: '已完成',\n        className: 'status-completed',\n        iconColor: 'text-green-400',\n        bgColor: 'bg-green-500/10',\n        borderColor: 'border-green-500/20',\n        glowColor: 'shadow-green-500/20'\n      },\n      'running': {\n        icon: animated ? Loader2 : Play,\n        text: '运行中',\n        className: 'status-running',\n        iconColor: 'text-blue-400',\n        bgColor: 'bg-blue-500/10',\n        borderColor: 'border-blue-500/20',\n        glowColor: 'shadow-blue-500/20',\n        animate: animated ? 'animate-spin' : ''\n      },\n      'failed': {\n        icon: XCircle,\n        text: '失败',\n        className: 'status-failed',\n        iconColor: 'text-red-400',\n        bgColor: 'bg-red-500/10',\n        borderColor: 'border-red-500/20',\n        glowColor: 'shadow-red-500/20'\n      },\n      'pending': {\n        icon: Clock,\n        text: '等待中',\n        className: 'status-pending',\n        iconColor: 'text-yellow-400',\n        bgColor: 'bg-yellow-500/10',\n        borderColor: 'border-yellow-500/20',\n        glowColor: 'shadow-yellow-500/20'\n      },\n      'paused': {\n        icon: Pause,\n        text: '已暂停',\n        className: 'status-indicator',\n        iconColor: 'text-gray-400',\n        bgColor: 'bg-gray-500/10',\n        borderColor: 'border-gray-500/20',\n        glowColor: 'shadow-gray-500/20'\n      },\n      'created': {\n        icon: Zap,\n        text: '已创建',\n        className: 'status-indicator',\n        iconColor: 'text-cyan-400',\n        bgColor: 'bg-cyan-500/10',\n        borderColor: 'border-cyan-500/20',\n        glowColor: 'shadow-cyan-500/20'\n      },\n      'error': {\n        icon: AlertTriangle,\n        text: '错误',\n        className: 'status-failed',\n        iconColor: 'text-red-400',\n        bgColor: 'bg-red-500/10',\n        borderColor: 'border-red-500/20',\n        glowColor: 'shadow-red-500/20'\n      }\n    };\n\n    return configs[status.toLowerCase()] || configs['pending'];\n  };\n\n  const getSizeClasses = (size: string) => {\n    const sizes = {\n      'sm': {\n        container: 'px-2 py-1 text-xs',\n        icon: 'h-3 w-3',\n        text: 'text-xs'\n      },\n      'md': {\n        container: 'px-3 py-1.5 text-sm',\n        icon: 'h-4 w-4',\n        text: 'text-sm'\n      },\n      'lg': {\n        container: 'px-4 py-2 text-base',\n        icon: 'h-5 w-5',\n        text: 'text-base'\n      }\n    };\n\n    return sizes[size] || sizes['md'];\n  };\n\n  const config = getStatusConfig(status);\n  const sizeClasses = getSizeClasses(size);\n  const Icon = config.icon;\n\n  return (\n    <div className={`\n      inline-flex items-center gap-2 rounded-full font-medium transition-all duration-300\n      ${config.className}\n      ${config.bgColor}\n      ${config.borderColor}\n      ${sizeClasses.container}\n      border\n      ${animated ? 'hover:scale-105' : ''}\n      ${animated && config.glowColor ? `hover:shadow-lg ${config.glowColor}` : ''}\n    `}>\n      <Icon className={`\n        ${sizeClasses.icon} \n        ${config.iconColor}\n        ${config.animate || ''}\n      `} />\n      {showText && (\n        <span className={`${sizeClasses.text} font-medium`}>\n          {config.text}\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default StatusIndicator;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,KAAK,EACLC,IAAI,EACJC,GAAG,QACE,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAStB,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,MAAM;EACNC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,IAAI;EACfC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAIJ,MAAc,IAAK;IAC1C,MAAMK,OAA4B,GAAG;MACnC,WAAW,EAAE;QACXC,IAAI,EAAEjB,WAAW;QACjBkB,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,gBAAgB;QAC3BC,OAAO,EAAE,iBAAiB;QAC1BC,WAAW,EAAE,qBAAqB;QAClCC,SAAS,EAAE;MACb,CAAC;MACD,SAAS,EAAE;QACTN,IAAI,EAAEH,QAAQ,GAAGX,OAAO,GAAGG,IAAI;QAC/BY,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,gBAAgB;QAC3BC,SAAS,EAAE,eAAe;QAC1BC,OAAO,EAAE,gBAAgB;QACzBC,WAAW,EAAE,oBAAoB;QACjCC,SAAS,EAAE,oBAAoB;QAC/BC,OAAO,EAAEV,QAAQ,GAAG,cAAc,GAAG;MACvC,CAAC;MACD,QAAQ,EAAE;QACRG,IAAI,EAAEhB,OAAO;QACbiB,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,eAAe;QAC1BC,SAAS,EAAE,cAAc;QACzBC,OAAO,EAAE,eAAe;QACxBC,WAAW,EAAE,mBAAmB;QAChCC,SAAS,EAAE;MACb,CAAC;MACD,SAAS,EAAE;QACTN,IAAI,EAAEf,KAAK;QACXgB,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,gBAAgB;QAC3BC,SAAS,EAAE,iBAAiB;QAC5BC,OAAO,EAAE,kBAAkB;QAC3BC,WAAW,EAAE,sBAAsB;QACnCC,SAAS,EAAE;MACb,CAAC;MACD,QAAQ,EAAE;QACRN,IAAI,EAAEZ,KAAK;QACXa,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,eAAe;QAC1BC,OAAO,EAAE,gBAAgB;QACzBC,WAAW,EAAE,oBAAoB;QACjCC,SAAS,EAAE;MACb,CAAC;MACD,SAAS,EAAE;QACTN,IAAI,EAAEV,GAAG;QACTW,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,eAAe;QAC1BC,OAAO,EAAE,gBAAgB;QACzBC,WAAW,EAAE,oBAAoB;QACjCC,SAAS,EAAE;MACb,CAAC;MACD,OAAO,EAAE;QACPN,IAAI,EAAEb,aAAa;QACnBc,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,eAAe;QAC1BC,SAAS,EAAE,cAAc;QACzBC,OAAO,EAAE,eAAe;QACxBC,WAAW,EAAE,mBAAmB;QAChCC,SAAS,EAAE;MACb;IACF,CAAC;IAED,OAAOP,OAAO,CAACL,MAAM,CAACc,WAAW,CAAC,CAAC,CAAC,IAAIT,OAAO,CAAC,SAAS,CAAC;EAC5D,CAAC;EAED,MAAMU,cAAc,GAAId,IAAY,IAAK;IACvC,MAAMe,KAAK,GAAG;MACZ,IAAI,EAAE;QACJC,SAAS,EAAE,mBAAmB;QAC9BX,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACD,IAAI,EAAE;QACJU,SAAS,EAAE,qBAAqB;QAChCX,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR,CAAC;MACD,IAAI,EAAE;QACJU,SAAS,EAAE,qBAAqB;QAChCX,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE;MACR;IACF,CAAC;IAED,OAAOS,KAAK,CAACf,IAAI,CAAC,IAAIe,KAAK,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAME,MAAM,GAAGd,eAAe,CAACJ,MAAM,CAAC;EACtC,MAAMmB,WAAW,GAAGJ,cAAc,CAACd,IAAI,CAAC;EACxC,MAAMmB,IAAI,GAAGF,MAAM,CAACZ,IAAI;EAExB,oBACER,OAAA;IAAKU,SAAS,EAAE;AACpB;AACA,QAAQU,MAAM,CAACV,SAAS;AACxB,QAAQU,MAAM,CAACR,OAAO;AACtB,QAAQQ,MAAM,CAACP,WAAW;AAC1B,QAAQQ,WAAW,CAACF,SAAS;AAC7B;AACA,QAAQd,QAAQ,GAAG,iBAAiB,GAAG,EAAE;AACzC,QAAQA,QAAQ,IAAIe,MAAM,CAACN,SAAS,GAAG,mBAAmBM,MAAM,CAACN,SAAS,EAAE,GAAG,EAAE;AACjF,KAAM;IAAAS,QAAA,gBACAvB,OAAA,CAACsB,IAAI;MAACZ,SAAS,EAAE;AACvB,UAAUW,WAAW,CAACb,IAAI;AAC1B,UAAUY,MAAM,CAACT,SAAS;AAC1B,UAAUS,MAAM,CAACL,OAAO,IAAI,EAAE;AAC9B;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACJvB,QAAQ,iBACPJ,OAAA;MAAMU,SAAS,EAAE,GAAGW,WAAW,CAACZ,IAAI,cAAe;MAAAc,QAAA,EAChDH,MAAM,CAACX;IAAI;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACC,EAAA,GA9HI3B,eAA+C;AAgIrD,eAAeA,eAAe;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}