{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst PanelBottomInactive = createLucideIcon(\"PanelBottomInactive\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M14 15h1\",\n  key: \"171nev\"\n}], [\"path\", {\n  d: \"M19 15h2\",\n  key: \"1vnucp\"\n}], [\"path\", {\n  d: \"M3 15h2\",\n  key: \"8bym0q\"\n}], [\"path\", {\n  d: \"M9 15h1\",\n  key: \"1tg3ks\"\n}]]);\nexport { PanelBottomInactive as default };", "map": {"version": 3, "names": ["PanelBottomInactive", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/panel-bottom-inactive.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PanelBottomInactive\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xNCAxNWgxIiAvPgogIDxwYXRoIGQ9Ik0xOSAxNWgyIiAvPgogIDxwYXRoIGQ9Ik0zIDE1aDIiIC8+CiAgPHBhdGggZD0iTTkgMTVoMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/panel-bottom-inactive\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PanelBottomInactive = createLucideIcon('PanelBottomInactive', [\n  [\n    'rect',\n    { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' },\n  ],\n  ['path', { d: 'M14 15h1', key: '171nev' }],\n  ['path', { d: 'M19 15h2', key: '1vnucp' }],\n  ['path', { d: 'M3 15h2', key: '8bym0q' }],\n  ['path', { d: 'M9 15h1', key: '1tg3ks' }],\n]);\n\nexport default PanelBottomInactive;\n"], "mappings": ";;;;;AAaM,MAAAA,mBAAA,GAAsBC,gBAAA,CAAiB,qBAAuB,GAClE,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}