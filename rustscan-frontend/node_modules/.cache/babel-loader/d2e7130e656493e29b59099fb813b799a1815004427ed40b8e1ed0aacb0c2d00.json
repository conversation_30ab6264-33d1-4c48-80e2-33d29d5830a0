{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst DoorOpen = createLucideIcon(\"DoorOpen\", [[\"path\", {\n  d: \"M13 4h3a2 2 0 0 1 2 2v14\",\n  key: \"hrm0s9\"\n}], [\"path\", {\n  d: \"M2 20h3\",\n  key: \"1gaodv\"\n}], [\"path\", {\n  d: \"M13 20h9\",\n  key: \"s90cdi\"\n}], [\"path\", {\n  d: \"M10 12v.01\",\n  key: \"vx6srw\"\n}], [\"path\", {\n  d: \"M13 4.562v16.157a1 1 0 0 1-1.242.97L5 20V5.562a2 2 0 0 1 1.515-1.94l4-1A2 2 0 0 1 13 4.561Z\",\n  key: \"199qr4\"\n}]]);\nexport { DoorOpen as default };", "map": {"version": 3, "names": ["DoorOpen", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/door-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name DoorOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgNGgzYTIgMiAwIDAgMSAyIDJ2MTQiIC8+CiAgPHBhdGggZD0iTTIgMjBoMyIgLz4KICA8cGF0aCBkPSJNMTMgMjBoOSIgLz4KICA8cGF0aCBkPSJNMTAgMTJ2LjAxIiAvPgogIDxwYXRoIGQ9Ik0xMyA0LjU2MnYxNi4xNTdhMSAxIDAgMCAxLTEuMjQyLjk3TDUgMjBWNS41NjJhMiAyIDAgMCAxIDEuNTE1LTEuOTRsNC0xQTIgMiAwIDAgMSAxMyA0LjU2MVoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/door-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DoorOpen = createLucideIcon('DoorOpen', [\n  ['path', { d: 'M13 4h3a2 2 0 0 1 2 2v14', key: 'hrm0s9' }],\n  ['path', { d: 'M2 20h3', key: '1gaodv' }],\n  ['path', { d: 'M13 20h9', key: 's90cdi' }],\n  ['path', { d: 'M10 12v.01', key: 'vx6srw' }],\n  [\n    'path',\n    {\n      d: 'M13 4.562v16.157a1 1 0 0 1-1.242.97L5 20V5.562a2 2 0 0 1 1.515-1.94l4-1A2 2 0 0 1 13 4.561Z',\n      key: '199qr4',\n    },\n  ],\n]);\n\nexport default DoorOpen;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}