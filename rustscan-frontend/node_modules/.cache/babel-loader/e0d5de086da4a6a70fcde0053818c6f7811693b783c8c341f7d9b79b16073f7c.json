{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileAudio = createLucideIcon(\"FileAudio\", [[\"path\", {\n  d: \"M17.5 22h.5c.5 0 1-.2 1.4-.6.4-.4.6-.9.6-1.4V7.5L14.5 2H6c-.5 0-1 .2-1.4.6C4.2 3 4 3.5 4 4v3\",\n  key: \"1013sb\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"M10 20v-1a2 2 0 1 1 4 0v1a2 2 0 1 1-4 0Z\",\n  key: \"gqt63y\"\n}], [\"path\", {\n  d: \"M6 20v-1a2 2 0 1 0-4 0v1a2 2 0 1 0 4 0Z\",\n  key: \"cf7lqx\"\n}], [\"path\", {\n  d: \"M2 19v-3a6 6 0 0 1 12 0v3\",\n  key: \"1acxgf\"\n}]]);\nexport { FileAudio as default };", "map": {"version": 3, "names": ["FileAudio", "createLucideIcon", "d", "key", "points"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/file-audio.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileAudio\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcuNSAyMmguNWMuNSAwIDEtLjIgMS40LS42LjQtLjQuNi0uOS42LTEuNFY3LjVMMTQuNSAySDZjLS41IDAtMSAuMi0xLjQuNkM0LjIgMyA0IDMuNSA0IDR2MyIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNCAyIDE0IDggMjAgOCIgLz4KICA8cGF0aCBkPSJNMTAgMjB2LTFhMiAyIDAgMSAxIDQgMHYxYTIgMiAwIDEgMS00IDBaIiAvPgogIDxwYXRoIGQ9Ik02IDIwdi0xYTIgMiAwIDEgMC00IDB2MWEyIDIgMCAxIDAgNCAwWiIgLz4KICA8cGF0aCBkPSJNMiAxOXYtM2E2IDYgMCAwIDEgMTIgMHYzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-audio\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileAudio = createLucideIcon('FileAudio', [\n  [\n    'path',\n    {\n      d: 'M17.5 22h.5c.5 0 1-.2 1.4-.6.4-.4.6-.9.6-1.4V7.5L14.5 2H6c-.5 0-1 .2-1.4.6C4.2 3 4 3.5 4 4v3',\n      key: '1013sb',\n    },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['path', { d: 'M10 20v-1a2 2 0 1 1 4 0v1a2 2 0 1 1-4 0Z', key: 'gqt63y' }],\n  ['path', { d: 'M6 20v-1a2 2 0 1 0-4 0v1a2 2 0 1 0 4 0Z', key: 'cf7lqx' }],\n  ['path', { d: 'M2 19v-3a6 6 0 0 1 12 0v3', key: '1acxgf' }],\n]);\n\nexport default FileAudio;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}