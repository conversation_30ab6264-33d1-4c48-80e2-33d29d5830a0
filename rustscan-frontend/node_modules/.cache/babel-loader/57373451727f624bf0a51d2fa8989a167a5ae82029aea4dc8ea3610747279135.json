{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/ProgressBar.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProgressBar = ({\n  progress,\n  status = 'running',\n  showPercentage = true,\n  showSteps = false,\n  size = 'md',\n  animated = true,\n  variant = 'cyber'\n}) => {\n  const getProgressData = () => {\n    if (typeof progress === 'number') {\n      return {\n        percentage: progress,\n        completed_steps: 0,\n        total_steps: 0\n      };\n    }\n    return progress;\n  };\n  const progressData = getProgressData();\n  const percentage = Math.min(100, Math.max(0, progressData.percentage));\n  const getSizeClasses = () => {\n    const sizes = {\n      'sm': 'h-1.5',\n      'md': 'h-2.5',\n      'lg': 'h-4'\n    };\n    return sizes[size] || sizes['md'];\n  };\n  const getVariantClasses = () => {\n    const variants = {\n      'default': {\n        container: 'bg-secondary rounded-full',\n        bar: 'bg-primary rounded-full transition-all duration-500 ease-out',\n        glow: ''\n      },\n      'cyber': {\n        container: 'bg-secondary/50 rounded-full border border-border/30',\n        bar: 'bg-gradient-to-r from-primary to-cyan-400 rounded-full transition-all duration-500 ease-out relative overflow-hidden',\n        glow: 'shadow-lg shadow-primary/20'\n      },\n      'terminal': {\n        container: 'bg-black/50 rounded border border-green-500/30',\n        bar: 'bg-gradient-to-r from-green-400 to-green-500 transition-all duration-500 ease-out relative overflow-hidden',\n        glow: 'shadow-lg shadow-green-500/30'\n      }\n    };\n    return variants[variant] || variants['cyber'];\n  };\n  const getStatusColor = () => {\n    const colors = {\n      'completed': 'from-green-400 to-green-500',\n      'running': 'from-primary to-cyan-400',\n      'failed': 'from-red-400 to-red-500',\n      'pending': 'from-yellow-400 to-yellow-500',\n      'paused': 'from-gray-400 to-gray-500'\n    };\n    return colors[status] || colors['running'];\n  };\n  const variantClasses = getVariantClasses();\n  const sizeClass = getSizeClasses();\n  const statusColor = getStatusColor();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-2\",\n    children: [(showPercentage || showSteps) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between text-sm\",\n      children: [showSteps && progressData.total_steps > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-muted-foreground font-mono\",\n        children: [\"\\u6B65\\u9AA4 \", progressData.completed_steps, \"/\", progressData.total_steps]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 13\n      }, this), showPercentage && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-foreground font-medium\",\n        children: [percentage.toFixed(1), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n        relative overflow-hidden\n        ${variantClasses.container}\n        ${sizeClass}\n        ${variantClasses.glow}\n      `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n            h-full transition-all duration-700 ease-out\n            ${variant === 'cyber' || variant === 'terminal' ? `bg-gradient-to-r ${statusColor}` : variantClasses.bar}\n          `,\n        style: {\n          width: `${percentage}%`\n        },\n        children: [animated && variant === 'cyber' && status === 'running' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"scan-progress absolute inset-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-scan-line\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this), animated && variant === 'terminal' && status === 'running' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-transparent via-green-300/20 to-transparent animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), variant === 'cyber' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full w-full bg-gradient-to-r from-transparent via-primary/10 to-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), status === 'running' && animated && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-1.5 h-1.5 bg-primary rounded-full animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-mono\",\n        children: \"\\u626B\\u63CF\\u8FDB\\u884C\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_c = ProgressBar;\nexport default ProgressBar;\nvar _c;\n$RefreshReg$(_c, \"ProgressBar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ProgressBar", "progress", "status", "showPercentage", "showSteps", "size", "animated", "variant", "getProgressData", "percentage", "completed_steps", "total_steps", "progressData", "Math", "min", "max", "getSizeClasses", "sizes", "getVariantClasses", "variants", "container", "bar", "glow", "getStatusColor", "colors", "variantClasses", "sizeClass", "statusColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toFixed", "style", "width", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/ProgressBar.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ProgressBarProps {\n  progress: number | { completed_steps: number; percentage: number; total_steps: number };\n  status?: string;\n  showPercentage?: boolean;\n  showSteps?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n  animated?: boolean;\n  variant?: 'default' | 'cyber' | 'terminal';\n}\n\nconst ProgressBar: React.FC<ProgressBarProps> = ({\n  progress,\n  status = 'running',\n  showPercentage = true,\n  showSteps = false,\n  size = 'md',\n  animated = true,\n  variant = 'cyber'\n}) => {\n  const getProgressData = () => {\n    if (typeof progress === 'number') {\n      return {\n        percentage: progress,\n        completed_steps: 0,\n        total_steps: 0\n      };\n    }\n    return progress;\n  };\n\n  const progressData = getProgressData();\n  const percentage = Math.min(100, Math.max(0, progressData.percentage));\n\n  const getSizeClasses = () => {\n    const sizes = {\n      'sm': 'h-1.5',\n      'md': 'h-2.5',\n      'lg': 'h-4'\n    };\n    return sizes[size] || sizes['md'];\n  };\n\n  const getVariantClasses = () => {\n    const variants = {\n      'default': {\n        container: 'bg-secondary rounded-full',\n        bar: 'bg-primary rounded-full transition-all duration-500 ease-out',\n        glow: ''\n      },\n      'cyber': {\n        container: 'bg-secondary/50 rounded-full border border-border/30',\n        bar: 'bg-gradient-to-r from-primary to-cyan-400 rounded-full transition-all duration-500 ease-out relative overflow-hidden',\n        glow: 'shadow-lg shadow-primary/20'\n      },\n      'terminal': {\n        container: 'bg-black/50 rounded border border-green-500/30',\n        bar: 'bg-gradient-to-r from-green-400 to-green-500 transition-all duration-500 ease-out relative overflow-hidden',\n        glow: 'shadow-lg shadow-green-500/30'\n      }\n    };\n    return variants[variant] || variants['cyber'];\n  };\n\n  const getStatusColor = () => {\n    const colors: Record<string, string> = {\n      'completed': 'from-green-400 to-green-500',\n      'running': 'from-primary to-cyan-400',\n      'failed': 'from-red-400 to-red-500',\n      'pending': 'from-yellow-400 to-yellow-500',\n      'paused': 'from-gray-400 to-gray-500'\n    };\n    return colors[status] || colors['running'];\n  };\n\n  const variantClasses = getVariantClasses();\n  const sizeClass = getSizeClasses();\n  const statusColor = getStatusColor();\n\n  return (\n    <div className=\"space-y-2\">\n      {/* 进度信息 */}\n      {(showPercentage || showSteps) && (\n        <div className=\"flex items-center justify-between text-sm\">\n          {showSteps && progressData.total_steps > 0 && (\n            <span className=\"text-muted-foreground font-mono\">\n              步骤 {progressData.completed_steps}/{progressData.total_steps}\n            </span>\n          )}\n          {showPercentage && (\n            <span className=\"text-foreground font-medium\">\n              {percentage.toFixed(1)}%\n            </span>\n          )}\n        </div>\n      )}\n\n      {/* 进度条 */}\n      <div className={`\n        relative overflow-hidden\n        ${variantClasses.container}\n        ${sizeClass}\n        ${variantClasses.glow}\n      `}>\n        {/* 进度填充 */}\n        <div\n          className={`\n            h-full transition-all duration-700 ease-out\n            ${variant === 'cyber' || variant === 'terminal' ? `bg-gradient-to-r ${statusColor}` : variantClasses.bar}\n          `}\n          style={{ width: `${percentage}%` }}\n        >\n          {/* 扫描线动画 */}\n          {animated && variant === 'cyber' && status === 'running' && (\n            <div className=\"absolute inset-0 overflow-hidden\">\n              <div className=\"scan-progress absolute inset-0\">\n                <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-scan-line\"></div>\n              </div>\n            </div>\n          )}\n\n          {/* 终端风格动画 */}\n          {animated && variant === 'terminal' && status === 'running' && (\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-green-300/20 to-transparent animate-pulse\"></div>\n          )}\n        </div>\n\n        {/* 背景网格效果 (仅cyber变体) */}\n        {variant === 'cyber' && (\n          <div className=\"absolute inset-0 opacity-10\">\n            <div className=\"h-full w-full bg-gradient-to-r from-transparent via-primary/10 to-transparent\"></div>\n          </div>\n        )}\n      </div>\n\n      {/* 状态文本 */}\n      {status === 'running' && animated && (\n        <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-pulse\"></div>\n          <span className=\"font-mono\">扫描进行中...</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProgressBar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY1B,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ;EACRC,MAAM,GAAG,SAAS;EAClBC,cAAc,GAAG,IAAI;EACrBC,SAAS,GAAG,KAAK;EACjBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,IAAI;EACfC,OAAO,GAAG;AACZ,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,OAAOP,QAAQ,KAAK,QAAQ,EAAE;MAChC,OAAO;QACLQ,UAAU,EAAER,QAAQ;QACpBS,eAAe,EAAE,CAAC;QAClBC,WAAW,EAAE;MACf,CAAC;IACH;IACA,OAAOV,QAAQ;EACjB,CAAC;EAED,MAAMW,YAAY,GAAGJ,eAAe,CAAC,CAAC;EACtC,MAAMC,UAAU,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,YAAY,CAACH,UAAU,CAAC,CAAC;EAEtE,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG;MACZ,IAAI,EAAE,OAAO;MACb,IAAI,EAAE,OAAO;MACb,IAAI,EAAE;IACR,CAAC;IACD,OAAOA,KAAK,CAACZ,IAAI,CAAC,IAAIY,KAAK,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,QAAQ,GAAG;MACf,SAAS,EAAE;QACTC,SAAS,EAAE,2BAA2B;QACtCC,GAAG,EAAE,8DAA8D;QACnEC,IAAI,EAAE;MACR,CAAC;MACD,OAAO,EAAE;QACPF,SAAS,EAAE,sDAAsD;QACjEC,GAAG,EAAE,sHAAsH;QAC3HC,IAAI,EAAE;MACR,CAAC;MACD,UAAU,EAAE;QACVF,SAAS,EAAE,gDAAgD;QAC3DC,GAAG,EAAE,4GAA4G;QACjHC,IAAI,EAAE;MACR;IACF,CAAC;IACD,OAAOH,QAAQ,CAACZ,OAAO,CAAC,IAAIY,QAAQ,CAAC,OAAO,CAAC;EAC/C,CAAC;EAED,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAA8B,GAAG;MACrC,WAAW,EAAE,6BAA6B;MAC1C,SAAS,EAAE,0BAA0B;MACrC,QAAQ,EAAE,yBAAyB;MACnC,SAAS,EAAE,+BAA+B;MAC1C,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOA,MAAM,CAACtB,MAAM,CAAC,IAAIsB,MAAM,CAAC,SAAS,CAAC;EAC5C,CAAC;EAED,MAAMC,cAAc,GAAGP,iBAAiB,CAAC,CAAC;EAC1C,MAAMQ,SAAS,GAAGV,cAAc,CAAC,CAAC;EAClC,MAAMW,WAAW,GAAGJ,cAAc,CAAC,CAAC;EAEpC,oBACExB,OAAA;IAAK6B,SAAS,EAAC,WAAW;IAAAC,QAAA,GAEvB,CAAC1B,cAAc,IAAIC,SAAS,kBAC3BL,OAAA;MAAK6B,SAAS,EAAC,2CAA2C;MAAAC,QAAA,GACvDzB,SAAS,IAAIQ,YAAY,CAACD,WAAW,GAAG,CAAC,iBACxCZ,OAAA;QAAM6B,SAAS,EAAC,iCAAiC;QAAAC,QAAA,GAAC,eAC7C,EAACjB,YAAY,CAACF,eAAe,EAAC,GAAC,EAACE,YAAY,CAACD,WAAW;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CACP,EACA9B,cAAc,iBACbJ,OAAA;QAAM6B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GAC1CpB,UAAU,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,GACzB;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDlC,OAAA;MAAK6B,SAAS,EAAE;AACtB;AACA,UAAUH,cAAc,CAACL,SAAS;AAClC,UAAUM,SAAS;AACnB,UAAUD,cAAc,CAACH,IAAI;AAC7B,OAAQ;MAAAO,QAAA,gBAEA9B,OAAA;QACE6B,SAAS,EAAE;AACrB;AACA,cAAcrB,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,UAAU,GAAG,oBAAoBoB,WAAW,EAAE,GAAGF,cAAc,CAACJ,GAAG;AACpH,WAAY;QACFc,KAAK,EAAE;UAAEC,KAAK,EAAE,GAAG3B,UAAU;QAAI,CAAE;QAAAoB,QAAA,GAGlCvB,QAAQ,IAAIC,OAAO,KAAK,OAAO,IAAIL,MAAM,KAAK,SAAS,iBACtDH,OAAA;UAAK6B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/C9B,OAAA;YAAK6B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C9B,OAAA;cAAK6B,SAAS,EAAC;YAAkG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA3B,QAAQ,IAAIC,OAAO,KAAK,UAAU,IAAIL,MAAM,KAAK,SAAS,iBACzDH,OAAA;UAAK6B,SAAS,EAAC;QAAkG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL1B,OAAO,KAAK,OAAO,iBAClBR,OAAA;QAAK6B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1C9B,OAAA;UAAK6B,SAAS,EAAC;QAA+E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL/B,MAAM,KAAK,SAAS,IAAII,QAAQ,iBAC/BP,OAAA;MAAK6B,SAAS,EAAC,uDAAuD;MAAAC,QAAA,gBACpE9B,OAAA;QAAK6B,SAAS,EAAC;MAAmD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzElC,OAAA;QAAM6B,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACI,EAAA,GArIIrC,WAAuC;AAuI7C,eAAeA,WAAW;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}