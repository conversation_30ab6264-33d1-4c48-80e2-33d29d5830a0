{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst PauseOctagon = createLucideIcon(\"PauseOctagon\", [[\"path\", {\n  d: \"M10 15V9\",\n  key: \"1lckn7\"\n}], [\"path\", {\n  d: \"M14 15V9\",\n  key: \"1muqhk\"\n}], [\"path\", {\n  d: \"M7.714 2h8.572L22 7.714v8.572L16.286 22H7.714L2 16.286V7.714L7.714 2z\",\n  key: \"1m7qra\"\n}]]);\nexport { PauseOctagon as default };", "map": {"version": 3, "names": ["PauseOctagon", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/pause-octagon.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PauseOctagon\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTVWOSIgLz4KICA8cGF0aCBkPSJNMTQgMTVWOSIgLz4KICA8cGF0aCBkPSJNNy43MTQgMmg4LjU3MkwyMiA3LjcxNHY4LjU3MkwxNi4yODYgMjJINy43MTRMMiAxNi4yODZWNy43MTRMNy43MTQgMnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pause-octagon\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PauseOctagon = createLucideIcon('PauseOctagon', [\n  ['path', { d: 'M10 15V9', key: '1lckn7' }],\n  ['path', { d: 'M14 15V9', key: '1muqhk' }],\n  [\n    'path',\n    {\n      d: 'M7.714 2h8.572L22 7.714v8.572L16.286 22H7.714L2 16.286V7.714L7.714 2z',\n      key: '1m7qra',\n    },\n  ],\n]);\n\nexport default PauseOctagon;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}