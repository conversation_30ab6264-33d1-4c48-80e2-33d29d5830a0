{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { Play, FileText, Target, Settings, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';\nimport { workflowApi } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreateWorkflow = () => {\n  _s();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const templateId = searchParams.get('template');\n  const [templates, setTemplates] = useState([]);\n  const [selectedTemplate, setSelectedTemplate] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    target: '',\n    context: {}\n  });\n  const [loading, setLoading] = useState(false);\n  const [templatesLoading, setTemplatesLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  useEffect(() => {\n    loadTemplates();\n  }, []);\n  useEffect(() => {\n    if (templateId && templates.length > 0) {\n      setSelectedTemplate(templateId);\n      const template = templates.find(t => t.id === templateId);\n      if (template) {\n        setFormData(prev => ({\n          ...prev,\n          name: `${template.name} - ${new Date().toLocaleString()}`\n        }));\n      }\n    }\n  }, [templateId, templates]);\n  const loadTemplates = async () => {\n    try {\n      setTemplatesLoading(true);\n      const response = await workflowApi.getWorkflowTemplates();\n      if (response.success && response.data) {\n        setTemplates(response.data.templates);\n      } else {\n        var _response$error;\n        setError(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || '加载模板失败');\n      }\n    } catch (err) {\n      setError('网络错误，请检查连接');\n    } finally {\n      setTemplatesLoading(false);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!selectedTemplate) {\n      setError('请选择一个工作流模板');\n      return;\n    }\n    if (!formData.name.trim()) {\n      setError('请输入工作流名称');\n      return;\n    }\n    if (!formData.target.trim()) {\n      setError('请输入扫描目标');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n      const request = {\n        workflow_id: selectedTemplate,\n        name: formData.name.trim(),\n        target: formData.target.trim(),\n        context: formData.context\n      };\n      const response = await workflowApi.createWorkflowInstance(request);\n      if (response.success && response.data) {\n        setSuccess('工作流实例创建成功！');\n        setTimeout(() => {\n          navigate(`/instances/${response.data.instance_id}`);\n        }, 1500);\n      } else {\n        var _response$error2;\n        setError(((_response$error2 = response.error) === null || _response$error2 === void 0 ? void 0 : _response$error2.message) || '创建工作流实例失败');\n      }\n    } catch (err) {\n      setError('网络错误，请检查连接');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const selectedTemplateData = templates.find(t => t.id === selectedTemplate);\n  const validateTarget = target => {\n    // 简单的目标验证\n    const ipRegex = /^(\\d{1,3}\\.){3}\\d{1,3}(\\/\\d{1,2})?$/;\n    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\\.[a-zA-Z]{2,}$/;\n    const urlRegex = /^https?:\\/\\/.+/;\n    return ipRegex.test(target) || domainRegex.test(target) || urlRegex.test(target);\n  };\n  if (templatesLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(Loader2, {\n        className: \"h-8 w-8 animate-spin text-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2 text-gray-600\",\n        children: \"\\u52A0\\u8F7D\\u6A21\\u677F...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto space-y-8 animate-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Target, {\n            className: \"h-12 w-12 text-primary animate-pulse-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 h-12 w-12 text-primary/20 animate-ping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent\",\n            children: \"\\u542F\\u52A8\\u5B89\\u5168\\u626B\\u63CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted-foreground text-lg font-mono mt-2\",\n            children: \"Configure & Deploy Security Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-muted-foreground max-w-2xl mx-auto\",\n        children: \"\\u9009\\u62E9\\u626B\\u63CF\\u6A21\\u677F\\uFF0C\\u914D\\u7F6E\\u76EE\\u6807\\u53C2\\u6570\\uFF0C\\u542F\\u52A8\\u4E13\\u4E1A\\u7EA7\\u7F51\\u7EDC\\u5B89\\u5168\\u626B\\u63CF\\u4EFB\\u52A1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center p-4 bg-success-50 border border-success-200 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        className: \"h-5 w-5 text-success-500 mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-success-700\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center p-4 bg-danger-50 border border-danger-200 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        className: \"h-5 w-5 text-danger-500 mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-danger-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"h-5 w-5 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\u9009\\u62E9\\u5DE5\\u4F5C\\u6D41\\u6A21\\u677F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: templates.map(template => /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-start p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"template\",\n              value: template.id,\n              checked: selectedTemplate === template.id,\n              onChange: e => setSelectedTemplate(e.target.value),\n              className: \"mt-1 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: template.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge-info\",\n                  children: [template.steps.length, \" \\u6B65\\u9AA4\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: template.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, template.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            className: \"h-5 w-5 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\u57FA\\u672C\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u5DE5\\u4F5C\\u6D41\\u540D\\u79F0 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.name,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                name: e.target.value\n              })),\n              className: \"input\",\n              placeholder: \"\\u8F93\\u5165\\u5DE5\\u4F5C\\u6D41\\u5B9E\\u4F8B\\u540D\\u79F0\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"\\u626B\\u63CF\\u76EE\\u6807 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.target,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                target: e.target.value\n              })),\n              className: `input ${formData.target && !validateTarget(formData.target) ? 'border-danger-300' : ''}`,\n              placeholder: \"\\u4F8B\\u5982: example.com, ***********, ***********/24\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: \"\\u652F\\u6301\\u57DF\\u540D\\u3001IP\\u5730\\u5740\\u3001IP\\u6BB5\\u6216URL\\u683C\\u5F0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), formData.target && !validateTarget(formData.target) && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-danger-600 mt-1\",\n              children: \"\\u76EE\\u6807\\u683C\\u5F0F\\u53EF\\u80FD\\u4E0D\\u6B63\\u786E\\uFF0C\\u8BF7\\u68C0\\u67E5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), selectedTemplateData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Target, {\n            className: \"h-5 w-5 text-primary-600 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\u6267\\u884C\\u8BA1\\u5212\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: selectedTemplateData.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mr-3\",\n              children: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: step.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500 bg-white px-2 py-1 rounded\",\n                  children: step.tool\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this), step.depends_on.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: [\"\\u4F9D\\u8D56: \", step.depends_on.join(', ')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this)]\n          }, step.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), selectedTemplateData.global_config && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-900 mb-2\",\n            children: \"\\u5168\\u5C40\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600\",\n            children: Object.entries(selectedTemplateData.global_config).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [key, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: String(value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 23\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => navigate('/templates'),\n          className: \"btn-secondary\",\n          disabled: loading,\n          children: \"\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn-primary\",\n          disabled: loading || !selectedTemplate || !formData.name.trim() || !formData.target.trim(),\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Loader2, {\n              className: \"h-4 w-4 mr-2 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), \"\\u521B\\u5EFA\\u4E2D...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), \"\\u521B\\u5EFA\\u5E76\\u542F\\u52A8\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateWorkflow, \"wOB/oNN2da9yGrq/CfUt7ou0L6I=\", false, function () {\n  return [useNavigate, useSearchParams];\n});\n_c = CreateWorkflow;\nexport default CreateWorkflow;\nvar _c;\n$RefreshReg$(_c, \"CreateWorkflow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSearchParams", "Play", "FileText", "Target", "Settings", "CheckCircle", "AlertCircle", "Loader2", "workflowApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreateWorkflow", "_s", "navigate", "searchParams", "templateId", "get", "templates", "setTemplates", "selectedTemplate", "setSelectedTemplate", "formData", "setFormData", "name", "target", "context", "loading", "setLoading", "templatesLoading", "setTemplatesLoading", "error", "setError", "success", "setSuccess", "loadTemplates", "length", "template", "find", "t", "id", "prev", "Date", "toLocaleString", "response", "getWorkflowTemplates", "data", "_response$error", "message", "err", "handleSubmit", "e", "preventDefault", "trim", "request", "workflow_id", "createWorkflowInstance", "setTimeout", "instance_id", "_response$error2", "selectedTemplateData", "validate<PERSON><PERSON><PERSON>", "ipRegex", "domainRegex", "urlRegex", "test", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "map", "type", "value", "checked", "onChange", "steps", "description", "placeholder", "required", "step", "index", "tool", "depends_on", "join", "global_config", "Object", "entries", "key", "String", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/CreateWorkflow.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { \n  Play, \n  FileText, \n  Target, \n  Settings, \n  CheckCircle,\n  AlertCircle,\n  Loader2\n} from 'lucide-react';\nimport { workflowApi, WorkflowDefinition, CreateWorkflowInstanceRequest } from '../services/api';\n\nconst CreateWorkflow: React.FC = () => {\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const templateId = searchParams.get('template');\n\n  const [templates, setTemplates] = useState<WorkflowDefinition[]>([]);\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('');\n  const [formData, setFormData] = useState({\n    name: '',\n    target: '',\n    context: {}\n  });\n  const [loading, setLoading] = useState(false);\n  const [templatesLoading, setTemplatesLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadTemplates();\n  }, []);\n\n  useEffect(() => {\n    if (templateId && templates.length > 0) {\n      setSelectedTemplate(templateId);\n      const template = templates.find(t => t.id === templateId);\n      if (template) {\n        setFormData(prev => ({\n          ...prev,\n          name: `${template.name} - ${new Date().toLocaleString()}`\n        }));\n      }\n    }\n  }, [templateId, templates]);\n\n  const loadTemplates = async () => {\n    try {\n      setTemplatesLoading(true);\n      const response = await workflowApi.getWorkflowTemplates();\n      if (response.success && response.data) {\n        setTemplates(response.data.templates);\n      } else {\n        setError(response.error?.message || '加载模板失败');\n      }\n    } catch (err) {\n      setError('网络错误，请检查连接');\n    } finally {\n      setTemplatesLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!selectedTemplate) {\n      setError('请选择一个工作流模板');\n      return;\n    }\n\n    if (!formData.name.trim()) {\n      setError('请输入工作流名称');\n      return;\n    }\n\n    if (!formData.target.trim()) {\n      setError('请输入扫描目标');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const request: CreateWorkflowInstanceRequest = {\n        workflow_id: selectedTemplate,\n        name: formData.name.trim(),\n        target: formData.target.trim(),\n        context: formData.context\n      };\n\n      const response = await workflowApi.createWorkflowInstance(request);\n      \n      if (response.success && response.data) {\n        setSuccess('工作流实例创建成功！');\n        setTimeout(() => {\n          navigate(`/instances/${response.data!.instance_id}`);\n        }, 1500);\n      } else {\n        setError(response.error?.message || '创建工作流实例失败');\n      }\n    } catch (err) {\n      setError('网络错误，请检查连接');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const selectedTemplateData = templates.find(t => t.id === selectedTemplate);\n\n  const validateTarget = (target: string) => {\n    // 简单的目标验证\n    const ipRegex = /^(\\d{1,3}\\.){3}\\d{1,3}(\\/\\d{1,2})?$/;\n    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\\.[a-zA-Z]{2,}$/;\n    const urlRegex = /^https?:\\/\\/.+/;\n    \n    return ipRegex.test(target) || domainRegex.test(target) || urlRegex.test(target);\n  };\n\n  if (templatesLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <Loader2 className=\"h-8 w-8 animate-spin text-primary-600\" />\n        <span className=\"ml-2 text-gray-600\">加载模板...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8 animate-fade-in\">\n      {/* 页面标题 */}\n      <div className=\"text-center space-y-4\">\n        <div className=\"flex items-center justify-center space-x-3\">\n          <div className=\"relative\">\n            <Target className=\"h-12 w-12 text-primary animate-pulse-glow\" />\n            <div className=\"absolute inset-0 h-12 w-12 text-primary/20 animate-ping\"></div>\n          </div>\n          <div>\n            <h1 className=\"text-4xl font-bold bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent\">\n              启动安全扫描\n            </h1>\n            <p className=\"text-muted-foreground text-lg font-mono mt-2\">\n              Configure & Deploy Security Scan\n            </p>\n          </div>\n        </div>\n        <p className=\"text-muted-foreground max-w-2xl mx-auto\">\n          选择扫描模板，配置目标参数，启动专业级网络安全扫描任务\n        </p>\n      </div>\n\n      {/* 成功/错误消息 */}\n      {success && (\n        <div className=\"flex items-center p-4 bg-success-50 border border-success-200 rounded-lg\">\n          <CheckCircle className=\"h-5 w-5 text-success-500 mr-2\" />\n          <span className=\"text-success-700\">{success}</span>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"flex items-center p-4 bg-danger-50 border border-danger-200 rounded-lg\">\n          <AlertCircle className=\"h-5 w-5 text-danger-500 mr-2\" />\n          <span className=\"text-danger-700\">{error}</span>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* 选择模板 */}\n        <div className=\"card\">\n          <div className=\"flex items-center mb-4\">\n            <FileText className=\"h-5 w-5 text-primary-600 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">选择工作流模板</h3>\n          </div>\n          \n          <div className=\"space-y-3\">\n            {templates.map((template) => (\n              <label key={template.id} className=\"flex items-start p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50\">\n                <input\n                  type=\"radio\"\n                  name=\"template\"\n                  value={template.id}\n                  checked={selectedTemplate === template.id}\n                  onChange={(e) => setSelectedTemplate(e.target.value)}\n                  className=\"mt-1 mr-3\"\n                />\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <h4 className=\"font-medium text-gray-900\">{template.name}</h4>\n                    <span className=\"badge-info\">{template.steps.length} 步骤</span>\n                  </div>\n                  <p className=\"text-sm text-gray-600\">{template.description}</p>\n                </div>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* 基本配置 */}\n        <div className=\"card\">\n          <div className=\"flex items-center mb-4\">\n            <Settings className=\"h-5 w-5 text-primary-600 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">基本配置</h3>\n          </div>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                工作流名称 *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                className=\"input\"\n                placeholder=\"输入工作流实例名称\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                扫描目标 *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.target}\n                onChange={(e) => setFormData(prev => ({ ...prev, target: e.target.value }))}\n                className={`input ${formData.target && !validateTarget(formData.target) ? 'border-danger-300' : ''}`}\n                placeholder=\"例如: example.com, ***********, ***********/24\"\n                required\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">\n                支持域名、IP地址、IP段或URL格式\n              </p>\n              {formData.target && !validateTarget(formData.target) && (\n                <p className=\"text-xs text-danger-600 mt-1\">\n                  目标格式可能不正确，请检查\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* 模板详情 */}\n        {selectedTemplateData && (\n          <div className=\"card\">\n            <div className=\"flex items-center mb-4\">\n              <Target className=\"h-5 w-5 text-primary-600 mr-2\" />\n              <h3 className=\"text-lg font-medium text-gray-900\">执行计划</h3>\n            </div>\n            \n            <div className=\"space-y-3\">\n              {selectedTemplateData.steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mr-3\">\n                    {index + 1}\n                  </div>\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className=\"font-medium text-gray-900\">{step.name}</h4>\n                      <span className=\"text-xs text-gray-500 bg-white px-2 py-1 rounded\">\n                        {step.tool}\n                      </span>\n                    </div>\n                    {step.depends_on.length > 0 && (\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        依赖: {step.depends_on.join(', ')}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {selectedTemplateData.global_config && (\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                <h4 className=\"text-sm font-medium text-gray-900 mb-2\">全局配置</h4>\n                <div className=\"text-xs text-gray-600\">\n                  {Object.entries(selectedTemplateData.global_config).map(([key, value]) => (\n                    <div key={key} className=\"flex justify-between\">\n                      <span>{key}:</span>\n                      <span>{String(value)}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* 提交按钮 */}\n        <div className=\"flex justify-end space-x-3\">\n          <button\n            type=\"button\"\n            onClick={() => navigate('/templates')}\n            className=\"btn-secondary\"\n            disabled={loading}\n          >\n            取消\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn-primary\"\n            disabled={loading || !selectedTemplate || !formData.name.trim() || !formData.target.trim()}\n          >\n            {loading ? (\n              <>\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                创建中...\n              </>\n            ) : (\n              <>\n                <Play className=\"h-4 w-4 mr-2\" />\n                创建并启动\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default CreateWorkflow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,SACEC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,OAAO,QACF,cAAc;AACrB,SAASC,WAAW,QAA2D,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjG,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,YAAY,CAAC,GAAGhB,eAAe,CAAC,CAAC;EACxC,MAAMiB,UAAU,GAAGD,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC;EAE/C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAuB,EAAE,CAAC;EACpE,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC;IACvC4B,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,CAAC;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAgB,IAAI,CAAC;EAE3DC,SAAS,CAAC,MAAM;IACdsC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAENtC,SAAS,CAAC,MAAM;IACd,IAAImB,UAAU,IAAIE,SAAS,CAACkB,MAAM,GAAG,CAAC,EAAE;MACtCf,mBAAmB,CAACL,UAAU,CAAC;MAC/B,MAAMqB,QAAQ,GAAGnB,SAAS,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKxB,UAAU,CAAC;MACzD,IAAIqB,QAAQ,EAAE;QACZd,WAAW,CAACkB,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPjB,IAAI,EAAE,GAAGa,QAAQ,CAACb,IAAI,MAAM,IAAIkB,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC,EAAE,CAAC3B,UAAU,EAAEE,SAAS,CAAC,CAAC;EAE3B,MAAMiB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFL,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMc,QAAQ,GAAG,MAAMrC,WAAW,CAACsC,oBAAoB,CAAC,CAAC;MACzD,IAAID,QAAQ,CAACX,OAAO,IAAIW,QAAQ,CAACE,IAAI,EAAE;QACrC3B,YAAY,CAACyB,QAAQ,CAACE,IAAI,CAAC5B,SAAS,CAAC;MACvC,CAAC,MAAM;QAAA,IAAA6B,eAAA;QACLf,QAAQ,CAAC,EAAAe,eAAA,GAAAH,QAAQ,CAACb,KAAK,cAAAgB,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZjB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,SAAS;MACRF,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAChC,gBAAgB,EAAE;MACrBY,QAAQ,CAAC,YAAY,CAAC;MACtB;IACF;IAEA,IAAI,CAACV,QAAQ,CAACE,IAAI,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACzBrB,QAAQ,CAAC,UAAU,CAAC;MACpB;IACF;IAEA,IAAI,CAACV,QAAQ,CAACG,MAAM,CAAC4B,IAAI,CAAC,CAAC,EAAE;MAC3BrB,QAAQ,CAAC,SAAS,CAAC;MACnB;IACF;IAEA,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMsB,OAAsC,GAAG;QAC7CC,WAAW,EAAEnC,gBAAgB;QAC7BI,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAAC6B,IAAI,CAAC,CAAC;QAC1B5B,MAAM,EAAEH,QAAQ,CAACG,MAAM,CAAC4B,IAAI,CAAC,CAAC;QAC9B3B,OAAO,EAAEJ,QAAQ,CAACI;MACpB,CAAC;MAED,MAAMkB,QAAQ,GAAG,MAAMrC,WAAW,CAACiD,sBAAsB,CAACF,OAAO,CAAC;MAElE,IAAIV,QAAQ,CAACX,OAAO,IAAIW,QAAQ,CAACE,IAAI,EAAE;QACrCZ,UAAU,CAAC,YAAY,CAAC;QACxBuB,UAAU,CAAC,MAAM;UACf3C,QAAQ,CAAC,cAAc8B,QAAQ,CAACE,IAAI,CAAEY,WAAW,EAAE,CAAC;QACtD,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QAAA,IAAAC,gBAAA;QACL3B,QAAQ,CAAC,EAAA2B,gBAAA,GAAAf,QAAQ,CAACb,KAAK,cAAA4B,gBAAA,uBAAdA,gBAAA,CAAgBX,OAAO,KAAI,WAAW,CAAC;MAClD;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZjB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,oBAAoB,GAAG1C,SAAS,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKpB,gBAAgB,CAAC;EAE3E,MAAMyC,cAAc,GAAIpC,MAAc,IAAK;IACzC;IACA,MAAMqC,OAAO,GAAG,qCAAqC;IACrD,MAAMC,WAAW,GAAG,0DAA0D;IAC9E,MAAMC,QAAQ,GAAG,gBAAgB;IAEjC,OAAOF,OAAO,CAACG,IAAI,CAACxC,MAAM,CAAC,IAAIsC,WAAW,CAACE,IAAI,CAACxC,MAAM,CAAC,IAAIuC,QAAQ,CAACC,IAAI,CAACxC,MAAM,CAAC;EAClF,CAAC;EAED,IAAII,gBAAgB,EAAE;IACpB,oBACEpB,OAAA;MAAKyD,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD1D,OAAA,CAACH,OAAO;QAAC4D,SAAS,EAAC;MAAuC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7D9D,OAAA;QAAMyD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACE9D,OAAA;IAAKyD,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAE1D1D,OAAA;MAAKyD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpC1D,OAAA;QAAKyD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzD1D,OAAA;UAAKyD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1D,OAAA,CAACP,MAAM;YAACgE,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChE9D,OAAA;YAAKyD,SAAS,EAAC;UAAyD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eACN9D,OAAA;UAAA0D,QAAA,gBACE1D,OAAA;YAAIyD,SAAS,EAAC,4FAA4F;YAAAC,QAAA,EAAC;UAE3G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9D,OAAA;YAAGyD,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN9D,OAAA;QAAGyD,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGLtC,OAAO,iBACNxB,OAAA;MAAKyD,SAAS,EAAC,0EAA0E;MAAAC,QAAA,gBACvF1D,OAAA,CAACL,WAAW;QAAC8D,SAAS,EAAC;MAA+B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzD9D,OAAA;QAAMyD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAElC;MAAO;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACN,EAEAxC,KAAK,iBACJtB,OAAA;MAAKyD,SAAS,EAAC,wEAAwE;MAAAC,QAAA,gBACrF1D,OAAA,CAACJ,WAAW;QAAC6D,SAAS,EAAC;MAA8B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxD9D,OAAA;QAAMyD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEpC;MAAK;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACN,eAED9D,OAAA;MAAM+D,QAAQ,EAAEtB,YAAa;MAACgB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEjD1D,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA,CAACR,QAAQ;YAACiE,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtD9D,OAAA;YAAIyD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBjD,SAAS,CAACuD,GAAG,CAAEpC,QAAQ,iBACtB5B,OAAA;YAAyByD,SAAS,EAAC,wFAAwF;YAAAC,QAAA,gBACzH1D,OAAA;cACEiE,IAAI,EAAC,OAAO;cACZlD,IAAI,EAAC,UAAU;cACfmD,KAAK,EAAEtC,QAAQ,CAACG,EAAG;cACnBoC,OAAO,EAAExD,gBAAgB,KAAKiB,QAAQ,CAACG,EAAG;cAC1CqC,QAAQ,EAAG1B,CAAC,IAAK9B,mBAAmB,CAAC8B,CAAC,CAAC1B,MAAM,CAACkD,KAAK,CAAE;cACrDT,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACF9D,OAAA;cAAKyD,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB1D,OAAA;gBAAKyD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1D,OAAA;kBAAIyD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE9B,QAAQ,CAACb;gBAAI;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9D9D,OAAA;kBAAMyD,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAE9B,QAAQ,CAACyC,KAAK,CAAC1C,MAAM,EAAC,eAAG;gBAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACN9D,OAAA;gBAAGyD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE9B,QAAQ,CAAC0C;cAAW;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA,GAfIlC,QAAQ,CAACG,EAAE;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBhB,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA,CAACN,QAAQ;YAAC+D,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtD9D,OAAA;YAAIyD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAOyD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9D,OAAA;cACEiE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAErD,QAAQ,CAACE,IAAK;cACrBqD,QAAQ,EAAG1B,CAAC,IAAK5B,WAAW,CAACkB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjB,IAAI,EAAE2B,CAAC,CAAC1B,MAAM,CAACkD;cAAM,CAAC,CAAC,CAAE;cAC1ET,SAAS,EAAC,OAAO;cACjBc,WAAW,EAAC,wDAAW;cACvBC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAOyD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9D,OAAA;cACEiE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAErD,QAAQ,CAACG,MAAO;cACvBoD,QAAQ,EAAG1B,CAAC,IAAK5B,WAAW,CAACkB,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEhB,MAAM,EAAE0B,CAAC,CAAC1B,MAAM,CAACkD;cAAM,CAAC,CAAC,CAAE;cAC5ET,SAAS,EAAE,SAAS5C,QAAQ,CAACG,MAAM,IAAI,CAACoC,cAAc,CAACvC,QAAQ,CAACG,MAAM,CAAC,GAAG,mBAAmB,GAAG,EAAE,EAAG;cACrGuD,WAAW,EAAC,wDAA8C;cAC1DC,QAAQ;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9D,OAAA;cAAGyD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACHjD,QAAQ,CAACG,MAAM,IAAI,CAACoC,cAAc,CAACvC,QAAQ,CAACG,MAAM,CAAC,iBAClDhB,OAAA;cAAGyD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLX,oBAAoB,iBACnBnD,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1D,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA,CAACP,MAAM;YAACgE,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD9D,OAAA;YAAIyD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBP,oBAAoB,CAACkB,KAAK,CAACL,GAAG,CAAC,CAACS,IAAI,EAAEC,KAAK,kBAC1C1E,OAAA;YAAmByD,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBACxE1D,OAAA;cAAKyD,SAAS,EAAC,gHAAgH;cAAAC,QAAA,EAC5HgB,KAAK,GAAG;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACN9D,OAAA;cAAKyD,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB1D,OAAA;gBAAKyD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD1D,OAAA;kBAAIyD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEe,IAAI,CAAC1D;gBAAI;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1D9D,OAAA;kBAAMyD,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAC/De,IAAI,CAACE;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACLW,IAAI,CAACG,UAAU,CAACjD,MAAM,GAAG,CAAC,iBACzB3B,OAAA;gBAAGyD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,gBACpC,EAACe,IAAI,CAACG,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAhBEW,IAAI,CAAC1C,EAAE;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELX,oBAAoB,CAAC2B,aAAa,iBACjC9E,OAAA;UAAKyD,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C1D,OAAA;YAAIyD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE9D,OAAA;YAAKyD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACnCqB,MAAM,CAACC,OAAO,CAAC7B,oBAAoB,CAAC2B,aAAa,CAAC,CAACd,GAAG,CAAC,CAAC,CAACiB,GAAG,EAAEf,KAAK,CAAC,kBACnElE,OAAA;cAAeyD,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBAC7C1D,OAAA;gBAAA0D,QAAA,GAAOuB,GAAG,EAAC,GAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnB9D,OAAA;gBAAA0D,QAAA,EAAOwB,MAAM,CAAChB,KAAK;cAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAFpBmB,GAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGR,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGD9D,OAAA;QAAKyD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC1D,OAAA;UACEiE,IAAI,EAAC,QAAQ;UACbkB,OAAO,EAAEA,CAAA,KAAM9E,QAAQ,CAAC,YAAY,CAAE;UACtCoD,SAAS,EAAC,eAAe;UACzB2B,QAAQ,EAAElE,OAAQ;UAAAwC,QAAA,EACnB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9D,OAAA;UACEiE,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,aAAa;UACvB2B,QAAQ,EAAElE,OAAO,IAAI,CAACP,gBAAgB,IAAI,CAACE,QAAQ,CAACE,IAAI,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC/B,QAAQ,CAACG,MAAM,CAAC4B,IAAI,CAAC,CAAE;UAAAc,QAAA,EAE1FxC,OAAO,gBACNlB,OAAA,CAAAE,SAAA;YAAAwD,QAAA,gBACE1D,OAAA,CAACH,OAAO;cAAC4D,SAAS,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEnD;UAAA,eAAE,CAAC,gBAEH9D,OAAA,CAAAE,SAAA;YAAAwD,QAAA,gBACE1D,OAAA,CAACT,IAAI;cAACkE,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEnC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1D,EAAA,CArTID,cAAwB;EAAA,QACXd,WAAW,EACLC,eAAe;AAAA;AAAA+F,EAAA,GAFlClF,cAAwB;AAuT9B,eAAeA,cAAc;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}