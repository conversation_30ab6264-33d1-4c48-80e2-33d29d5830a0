{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Dashboard from './components/Dashboard';\nimport WorkflowTemplates from './components/WorkflowTemplates';\nimport WorkflowInstances from './components/WorkflowInstances';\nimport WorkflowDetail from './components/WorkflowDetail';\nimport CreateWorkflow from './components/CreateWorkflow';\nimport { NotificationProvider } from './components/NotificationSystem';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(NotificationProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/templates\",\n            element: /*#__PURE__*/_jsxDEV(WorkflowTemplates, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/instances\",\n            element: /*#__PURE__*/_jsxDEV(WorkflowInstances, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/instances/:id\",\n            element: /*#__PURE__*/_jsxDEV(WorkflowDetail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/create\",\n            element: /*#__PURE__*/_jsxDEV(CreateWorkflow, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Layout", "Dashboard", "WorkflowTemplates", "WorkflowInstances", "WorkflowDetail", "CreateWorkflow", "NotificationProvider", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Dashboard from './components/Dashboard';\nimport WorkflowTemplates from './components/WorkflowTemplates';\nimport WorkflowInstances from './components/WorkflowInstances';\nimport WorkflowDetail from './components/WorkflowDetail';\nimport CreateWorkflow from './components/CreateWorkflow';\nimport { NotificationProvider } from './components/NotificationSystem';\n\nfunction App() {\n  return (\n    <NotificationProvider>\n      <Router>\n        <Layout>\n          <Routes>\n            <Route path=\"/\" element={<Dashboard />} />\n            <Route path=\"/templates\" element={<WorkflowTemplates />} />\n            <Route path=\"/instances\" element={<WorkflowInstances />} />\n            <Route path=\"/instances/:id\" element={<WorkflowDetail />} />\n            <Route path=\"/create\" element={<CreateWorkflow />} />\n          </Routes>\n        </Layout>\n      </Router>\n    </NotificationProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,oBAAoB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,oBAAoB;IAAAI,QAAA,eACnBF,OAAA,CAACX,MAAM;MAAAa,QAAA,eACLF,OAAA,CAACR,MAAM;QAAAU,QAAA,eACLF,OAAA,CAACV,MAAM;UAAAY,QAAA,gBACLF,OAAA,CAACT,KAAK;YAACY,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEJ,OAAA,CAACP,SAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CR,OAAA,CAACT,KAAK;YAACY,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEJ,OAAA,CAACN,iBAAiB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3DR,OAAA,CAACT,KAAK;YAACY,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEJ,OAAA,CAACL,iBAAiB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3DR,OAAA,CAACT,KAAK;YAACY,IAAI,EAAC,gBAAgB;YAACC,OAAO,eAAEJ,OAAA,CAACJ,cAAc;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DR,OAAA,CAACT,KAAK;YAACY,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEJ,OAAA,CAACH,cAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B;AAACC,EAAA,GAhBQR,GAAG;AAkBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}