{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ShieldQuestion = createLucideIcon(\"ShieldQuestion\", [[\"path\", {\n  d: \"M12 17h.01\",\n  key: \"p32p05\"\n}], [\"path\", {\n  d: \"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10\",\n  key: \"1irkt0\"\n}], [\"path\", {\n  d: \"M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3\",\n  key: \"mhlwft\"\n}]]);\nexport { ShieldQuestion as default };", "map": {"version": 3, "names": ["ShieldQuestion", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/shield-question.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ShieldQuestion\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAyMnM4LTQgOC0xMFY1bC04LTMtOCAzdjdjMCA2IDggMTAgOCAxMCIgLz4KICA8cGF0aCBkPSJNOS4xIDlhMyAzIDAgMCAxIDUuODIgMWMwIDItMyAzLTMgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shield-question\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShieldQuestion = createLucideIcon('ShieldQuestion', [\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10', key: '1irkt0' }],\n  ['path', { d: 'M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3', key: 'mhlwft' }],\n]);\n\nexport default ShieldQuestion;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}