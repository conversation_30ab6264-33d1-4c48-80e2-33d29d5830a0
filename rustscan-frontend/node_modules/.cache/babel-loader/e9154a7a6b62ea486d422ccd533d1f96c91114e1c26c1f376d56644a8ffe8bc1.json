{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FileText, Play, Eye, ChevronRight, Loader2 } from 'lucide-react';\nimport { workflowApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowTemplates = () => {\n  _s();\n  const [templates, setTemplates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n  useEffect(() => {\n    loadTemplates();\n  }, []);\n  const loadTemplates = async () => {\n    try {\n      setLoading(true);\n      const response = await workflowApi.getWorkflowTemplates();\n      if (response.success && response.data) {\n        setTemplates(response.data.templates);\n      } else {\n        var _response$error;\n        setError(((_response$error = response.error) === null || _response$error === void 0 ? void 0 : _response$error.message) || '加载模板失败');\n      }\n    } catch (err) {\n      setError('网络错误，请检查连接');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getToolIcon = toolName => {\n    const icons = {\n      rustscan: '🔍',\n      nmap: '🌐',\n      subfinder: '🔎',\n      dnsx: '🌍',\n      httpx: '🌐',\n      nuclei: '🛡️'\n    };\n    return icons[toolName] || '⚡';\n  };\n  const getStepStatusColor = (index, totalSteps) => {\n    if (index === 0) return 'bg-primary-100 text-primary-700 border-primary-200';\n    if (index === totalSteps - 1) return 'bg-success-100 text-success-700 border-success-200';\n    return 'bg-gray-100 text-gray-700 border-gray-200';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Loader2, {\n          className: \"h-8 w-8 animate-spin text-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-2 text-gray-600\",\n          children: \"\\u52A0\\u8F7D\\u5DE5\\u4F5C\\u6D41\\u6A21\\u677F...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(FileText, {\n        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"\\u52A0\\u8F7D\\u5931\\u8D25\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadTemplates,\n        className: \"btn-primary\",\n        children: \"\\u91CD\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"\\u5DE5\\u4F5C\\u6D41\\u6A21\\u677F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u9009\\u62E9\\u9884\\u5B9A\\u4E49\\u7684\\u5DE5\\u4F5C\\u6D41\\u6A21\\u677F\\u6765\\u5FEB\\u901F\\u5F00\\u59CB\\u626B\\u63CF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/create\",\n        className: \"btn-primary\",\n        children: [/*#__PURE__*/_jsxDEV(Play, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), \"\\u521B\\u5EFA\\u5B9E\\u4F8B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: templates.map(template => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card hover:shadow-md transition-shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-primary-100 rounded-lg mr-3\",\n              children: /*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-6 w-6 text-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: template.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"\\u7248\\u672C \", template.version]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"badge-info\",\n            children: [template.steps.length, \" \\u6B65\\u9AA4\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: template.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-900 mb-2\",\n            children: \"\\u6267\\u884C\\u6B65\\u9AA4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [template.steps.slice(0, 3).map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: getToolIcon(step.tool)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-700\",\n                children: step.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-auto text-xs text-gray-500\",\n                children: step.tool\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 21\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 19\n            }, this)), template.steps.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: [\"... \\u8FD8\\u6709 \", template.steps.length - 3, \" \\u4E2A\\u6B65\\u9AA4\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), template.global_config && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-900 mb-2\",\n            children: \"\\u914D\\u7F6E\\u53C2\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-2 text-xs\",\n            children: Object.entries(template.global_config).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600\",\n                children: [key, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-900\",\n                children: String(value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 23\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedTemplate(template),\n            className: \"flex items-center text-sm text-primary-600 hover:text-primary-700\",\n            children: [/*#__PURE__*/_jsxDEV(Eye, {\n              className: \"h-4 w-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), \"\\u67E5\\u770B\\u8BE6\\u60C5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: `/create?template=${template.id}`,\n            className: \"btn-primary text-sm\",\n            children: [\"\\u4F7F\\u7528\\u6A21\\u677F\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n              className: \"h-4 w-4 ml-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)]\n      }, template.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), selectedTemplate && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75\",\n          onClick: () => setSelectedTemplate(null)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: selectedTemplate.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedTemplate(null),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-2\",\n                children: \"\\u63CF\\u8FF0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: selectedTemplate.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-2\",\n                children: \"\\u6267\\u884C\\u6B65\\u9AA4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: selectedTemplate.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start p-3 border border-gray-200 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `\n                          flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium mr-3\n                          ${getStepStatusColor(index, selectedTemplate.steps.length)}\n                        `,\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-medium text-gray-900\",\n                        children: step.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                        children: step.tool\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 27\n                    }, this), step.depends_on.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mb-2\",\n                      children: [\"\\u4F9D\\u8D56: \", step.depends_on.join(', ')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 29\n                    }, this), Object.keys(step.parameters).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: [\"\\u53C2\\u6570: \", Object.entries(step.parameters).map(([k, v]) => `${k}=${v}`).join(', ')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this)]\n                }, step.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), selectedTemplate.global_config && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-2\",\n                children: \"\\u5168\\u5C40\\u914D\\u7F6E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 p-3 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"text-sm text-gray-700\",\n                  children: JSON.stringify(selectedTemplate.global_config, null, 2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedTemplate(null),\n              className: \"btn-secondary\",\n              children: \"\\u5173\\u95ED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: `/create?template=${selectedTemplate.id}`,\n              className: \"btn-primary\",\n              onClick: () => setSelectedTemplate(null),\n              children: \"\\u4F7F\\u7528\\u6B64\\u6A21\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkflowTemplates, \"j0aXlCd1ksedlIvQuiGCGvBb2Kk=\");\n_c = WorkflowTemplates;\nexport default WorkflowTemplates;\nvar _c;\n$RefreshReg$(_c, \"WorkflowTemplates\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "FileText", "Play", "Eye", "ChevronRight", "Loader2", "workflowApi", "jsxDEV", "_jsxDEV", "WorkflowTemplates", "_s", "templates", "setTemplates", "loading", "setLoading", "error", "setError", "selectedTemplate", "setSelectedTemplate", "loadTemplates", "response", "getWorkflowTemplates", "success", "data", "_response$error", "message", "err", "getToolIcon", "toolName", "icons", "rustscan", "nmap", "subfinder", "dnsx", "httpx", "nuclei", "getStepStatusColor", "index", "totalSteps", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "to", "map", "template", "name", "version", "steps", "length", "description", "slice", "step", "tool", "id", "global_config", "Object", "entries", "key", "value", "String", "depends_on", "join", "keys", "parameters", "k", "v", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/WorkflowTemplates.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport {\n  FileText,\n  Play,\n  Eye,\n  ChevronRight,\n  Loader2\n} from 'lucide-react';\nimport { workflowApi, WorkflowDefinition } from '../services/api';\n\nconst WorkflowTemplates: React.FC = () => {\n  const [templates, setTemplates] = useState<WorkflowDefinition[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowDefinition | null>(null);\n\n  useEffect(() => {\n    loadTemplates();\n  }, []);\n\n  const loadTemplates = async () => {\n    try {\n      setLoading(true);\n      const response = await workflowApi.getWorkflowTemplates();\n      if (response.success && response.data) {\n        setTemplates(response.data.templates);\n      } else {\n        setError(response.error?.message || '加载模板失败');\n      }\n    } catch (err) {\n      setError('网络错误，请检查连接');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getToolIcon = (toolName: string) => {\n    const icons: Record<string, string> = {\n      rustscan: '🔍',\n      nmap: '🌐',\n      subfinder: '🔎',\n      dnsx: '🌍',\n      httpx: '🌐',\n      nuclei: '🛡️'\n    };\n    return icons[toolName] || '⚡';\n  };\n\n  const getStepStatusColor = (index: number, totalSteps: number) => {\n    if (index === 0) return 'bg-primary-100 text-primary-700 border-primary-200';\n    if (index === totalSteps - 1) return 'bg-success-100 text-success-700 border-success-200';\n    return 'bg-gray-100 text-gray-700 border-gray-200';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center justify-center py-12\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary-600\" />\n          <span className=\"ml-2 text-gray-600\">加载工作流模板...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">加载失败</h3>\n        <p className=\"text-gray-600 mb-4\">{error}</p>\n        <button onClick={loadTemplates} className=\"btn-primary\">\n          重试\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">工作流模板</h1>\n          <p className=\"text-gray-600\">选择预定义的工作流模板来快速开始扫描</p>\n        </div>\n        <Link to=\"/create\" className=\"btn-primary\">\n          <Play className=\"h-4 w-4 mr-2\" />\n          创建实例\n        </Link>\n      </div>\n\n      {/* 模板列表 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {templates.map((template) => (\n          <div key={template.id} className=\"card hover:shadow-md transition-shadow\">\n            <div className=\"flex items-start justify-between mb-4\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-primary-100 rounded-lg mr-3\">\n                  <FileText className=\"h-6 w-6 text-primary-600\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900\">{template.name}</h3>\n                  <p className=\"text-sm text-gray-600\">版本 {template.version}</p>\n                </div>\n              </div>\n              <span className=\"badge-info\">{template.steps.length} 步骤</span>\n            </div>\n\n            <p className=\"text-gray-600 mb-4\">{template.description}</p>\n\n            {/* 工作流步骤预览 */}\n            <div className=\"mb-4\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-2\">执行步骤</h4>\n              <div className=\"space-y-2\">\n                {template.steps.slice(0, 3).map((step, index) => (\n                  <div key={step.id} className=\"flex items-center text-sm\">\n                    <span className=\"mr-2\">{getToolIcon(step.tool)}</span>\n                    <span className=\"text-gray-700\">{step.name}</span>\n                    <span className=\"ml-auto text-xs text-gray-500\">{step.tool}</span>\n                  </div>\n                ))}\n                {template.steps.length > 3 && (\n                  <div className=\"text-sm text-gray-500\">\n                    ... 还有 {template.steps.length - 3} 个步骤\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* 全局配置 */}\n            {template.global_config && (\n              <div className=\"mb-4 p-3 bg-gray-50 rounded-lg\">\n                <h4 className=\"text-sm font-medium text-gray-900 mb-2\">配置参数</h4>\n                <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                  {Object.entries(template.global_config).map(([key, value]) => (\n                    <div key={key} className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">{key}:</span>\n                      <span className=\"text-gray-900\">{String(value)}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* 操作按钮 */}\n            <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n              <button\n                onClick={() => setSelectedTemplate(template)}\n                className=\"flex items-center text-sm text-primary-600 hover:text-primary-700\"\n              >\n                <Eye className=\"h-4 w-4 mr-1\" />\n                查看详情\n              </button>\n              <Link\n                to={`/create?template=${template.id}`}\n                className=\"btn-primary text-sm\"\n              >\n                使用模板\n                <ChevronRight className=\"h-4 w-4 ml-1\" />\n              </Link>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* 模板详情模态框 */}\n      {selectedTemplate && (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n          <div className=\"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\">\n            <div \n              className=\"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75\"\n              onClick={() => setSelectedTemplate(null)}\n            />\n            \n            <div className=\"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  {selectedTemplate.name}\n                </h3>\n                <button\n                  onClick={() => setSelectedTemplate(null)}\n                  className=\"text-gray-400 hover:text-gray-500\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-2\">描述</h4>\n                  <p className=\"text-gray-600\">{selectedTemplate.description}</p>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-2\">执行步骤</h4>\n                  <div className=\"space-y-3\">\n                    {selectedTemplate.steps.map((step, index) => (\n                      <div key={step.id} className=\"flex items-start p-3 border border-gray-200 rounded-lg\">\n                        <div className={`\n                          flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium mr-3\n                          ${getStepStatusColor(index, selectedTemplate.steps.length)}\n                        `}>\n                          {index + 1}\n                        </div>\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center justify-between mb-1\">\n                            <h5 className=\"font-medium text-gray-900\">{step.name}</h5>\n                            <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                              {step.tool}\n                            </span>\n                          </div>\n                          {step.depends_on.length > 0 && (\n                            <p className=\"text-xs text-gray-500 mb-2\">\n                              依赖: {step.depends_on.join(', ')}\n                            </p>\n                          )}\n                          {Object.keys(step.parameters).length > 0 && (\n                            <div className=\"text-xs text-gray-600\">\n                              参数: {Object.entries(step.parameters).map(([k, v]) => `${k}=${v}`).join(', ')}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {selectedTemplate.global_config && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-2\">全局配置</h4>\n                    <div className=\"bg-gray-50 p-3 rounded-lg\">\n                      <pre className=\"text-sm text-gray-700\">\n                        {JSON.stringify(selectedTemplate.global_config, null, 2)}\n                      </pre>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200\">\n                <button\n                  onClick={() => setSelectedTemplate(null)}\n                  className=\"btn-secondary\"\n                >\n                  关闭\n                </button>\n                <Link\n                  to={`/create?template=${selectedTemplate.id}`}\n                  className=\"btn-primary\"\n                  onClick={() => setSelectedTemplate(null)}\n                >\n                  使用此模板\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default WorkflowTemplates;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,YAAY,EACZC,OAAO,QACF,cAAc;AACrB,SAASC,WAAW,QAA4B,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAuB,EAAE,CAAC;EACpE,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAA4B,IAAI,CAAC;EAEzFC,SAAS,CAAC,MAAM;IACdoB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMd,WAAW,CAACe,oBAAoB,CAAC,CAAC;MACzD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCX,YAAY,CAACQ,QAAQ,CAACG,IAAI,CAACZ,SAAS,CAAC;MACvC,CAAC,MAAM;QAAA,IAAAa,eAAA;QACLR,QAAQ,CAAC,EAAAQ,eAAA,GAAAJ,QAAQ,CAACL,KAAK,cAAAS,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZV,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,WAAW,GAAIC,QAAgB,IAAK;IACxC,MAAMC,KAA6B,GAAG;MACpCC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE;IACV,CAAC;IACD,OAAON,KAAK,CAACD,QAAQ,CAAC,IAAI,GAAG;EAC/B,CAAC;EAED,MAAMQ,kBAAkB,GAAGA,CAACC,KAAa,EAAEC,UAAkB,KAAK;IAChE,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,oDAAoD;IAC5E,IAAIA,KAAK,KAAKC,UAAU,GAAG,CAAC,EAAE,OAAO,oDAAoD;IACzF,OAAO,2CAA2C;EACpD,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK+B,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBhC,OAAA;QAAK+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDhC,OAAA,CAACH,OAAO;UAACkC,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DpC,OAAA;UAAM+B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI7B,KAAK,EAAE;IACT,oBACEP,OAAA;MAAK+B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChC,OAAA,CAACP,QAAQ;QAACsC,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DpC,OAAA;QAAI+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChEpC,OAAA;QAAG+B,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEzB;MAAK;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7CpC,OAAA;QAAQqC,OAAO,EAAE1B,aAAc;QAACoB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAExD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEpC,OAAA;IAAK+B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhC,OAAA;MAAK+B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDhC,OAAA;QAAAgC,QAAA,gBACEhC,OAAA;UAAI+B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DpC,OAAA;UAAG+B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNpC,OAAA,CAACR,IAAI;QAAC8C,EAAE,EAAC,SAAS;QAACP,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxChC,OAAA,CAACN,IAAI;UAACqC,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNpC,OAAA;MAAK+B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnD7B,SAAS,CAACoC,GAAG,CAAEC,QAAQ,iBACtBxC,OAAA;QAAuB+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACvEhC,OAAA;UAAK+B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDhC,OAAA;YAAK+B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChChC,OAAA;cAAK+B,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDhC,OAAA,CAACP,QAAQ;gBAACsC,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNpC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAI+B,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAEQ,QAAQ,CAACC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxEpC,OAAA;gBAAG+B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,eAAG,EAACQ,QAAQ,CAACE,OAAO;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpC,OAAA;YAAM+B,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEQ,QAAQ,CAACG,KAAK,CAACC,MAAM,EAAC,eAAG;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAENpC,OAAA;UAAG+B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEQ,QAAQ,CAACK;QAAW;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAG5DpC,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhC,OAAA;YAAI+B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEpC,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvBQ,QAAQ,CAACG,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACP,GAAG,CAAC,CAACQ,IAAI,EAAElB,KAAK,kBAC1C7B,OAAA;cAAmB+B,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACtDhC,OAAA;gBAAM+B,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEb,WAAW,CAAC4B,IAAI,CAACC,IAAI;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDpC,OAAA;gBAAM+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEe,IAAI,CAACN;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDpC,OAAA;gBAAM+B,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAEe,IAAI,CAACC;cAAI;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAH1DW,IAAI,CAACE,EAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIZ,CACN,CAAC,EACDI,QAAQ,CAACG,KAAK,CAACC,MAAM,GAAG,CAAC,iBACxB5C,OAAA;cAAK+B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,mBAC9B,EAACQ,QAAQ,CAACG,KAAK,CAACC,MAAM,GAAG,CAAC,EAAC,qBACpC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLI,QAAQ,CAACU,aAAa,iBACrBlD,OAAA;UAAK+B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7ChC,OAAA;YAAI+B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEpC,OAAA;YAAK+B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAC5CmB,MAAM,CAACC,OAAO,CAACZ,QAAQ,CAACU,aAAa,CAAC,CAACX,GAAG,CAAC,CAAC,CAACc,GAAG,EAAEC,KAAK,CAAC,kBACvDtD,OAAA;cAAe+B,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBAC7ChC,OAAA;gBAAM+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAEqB,GAAG,EAAC,GAAC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CpC,OAAA;gBAAM+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEuB,MAAM,CAACD,KAAK;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAF9CiB,GAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGR,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDpC,OAAA;UAAK+B,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAC9EhC,OAAA;YACEqC,OAAO,EAAEA,CAAA,KAAM3B,mBAAmB,CAAC8B,QAAQ,CAAE;YAC7CT,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAE7EhC,OAAA,CAACL,GAAG;cAACoC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpC,OAAA,CAACR,IAAI;YACH8C,EAAE,EAAE,oBAAoBE,QAAQ,CAACS,EAAE,EAAG;YACtClB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,GAChC,0BAEC,eAAAhC,OAAA,CAACJ,YAAY;cAACmC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA,GAlEEI,QAAQ,CAACS,EAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmEhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL3B,gBAAgB,iBACfT,OAAA;MAAK+B,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDhC,OAAA;QAAK+B,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGhC,OAAA;UACE+B,SAAS,EAAC,4DAA4D;UACtEM,OAAO,EAAEA,CAAA,KAAM3B,mBAAmB,CAAC,IAAI;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEFpC,OAAA;UAAK+B,SAAS,EAAC,sIAAsI;UAAAC,QAAA,gBACnJhC,OAAA;YAAK+B,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDhC,OAAA;cAAI+B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAChDvB,gBAAgB,CAACgC;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACLpC,OAAA;cACEqC,OAAO,EAAEA,CAAA,KAAM3B,mBAAmB,CAAC,IAAI,CAAE;cACzCqB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC9C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpC,OAAA;YAAK+B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAI+B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDpC,OAAA;gBAAG+B,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEvB,gBAAgB,CAACoC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eAENpC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAI+B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDpC,OAAA;gBAAK+B,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBvB,gBAAgB,CAACkC,KAAK,CAACJ,GAAG,CAAC,CAACQ,IAAI,EAAElB,KAAK,kBACtC7B,OAAA;kBAAmB+B,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACnFhC,OAAA;oBAAK+B,SAAS,EAAE;AACxC;AACA,4BAA4BH,kBAAkB,CAACC,KAAK,EAAEpB,gBAAgB,CAACkC,KAAK,CAACC,MAAM,CAAC;AACpF,yBAA0B;oBAAAZ,QAAA,EACCH,KAAK,GAAG;kBAAC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNpC,OAAA;oBAAK+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBhC,OAAA;sBAAK+B,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDhC,OAAA;wBAAI+B,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAEe,IAAI,CAACN;sBAAI;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1DpC,OAAA;wBAAM+B,SAAS,EAAC,qDAAqD;wBAAAC,QAAA,EAClEe,IAAI,CAACC;sBAAI;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EACLW,IAAI,CAACS,UAAU,CAACZ,MAAM,GAAG,CAAC,iBACzB5C,OAAA;sBAAG+B,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,GAAC,gBACpC,EAACe,IAAI,CAACS,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CACJ,EACAe,MAAM,CAACO,IAAI,CAACX,IAAI,CAACY,UAAU,CAAC,CAACf,MAAM,GAAG,CAAC,iBACtC5C,OAAA;sBAAK+B,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,gBACjC,EAACmB,MAAM,CAACC,OAAO,CAACL,IAAI,CAACY,UAAU,CAAC,CAACpB,GAAG,CAAC,CAAC,CAACqB,CAAC,EAAEC,CAAC,CAAC,KAAK,GAAGD,CAAC,IAAIC,CAAC,EAAE,CAAC,CAACJ,IAAI,CAAC,IAAI,CAAC;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAxBEW,IAAI,CAACE,EAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL3B,gBAAgB,CAACyC,aAAa,iBAC7BlD,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAI+B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDpC,OAAA;gBAAK+B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxChC,OAAA;kBAAK+B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnC8B,IAAI,CAACC,SAAS,CAACtD,gBAAgB,CAACyC,aAAa,EAAE,IAAI,EAAE,CAAC;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENpC,OAAA;YAAK+B,SAAS,EAAC,+DAA+D;YAAAC,QAAA,gBAC5EhC,OAAA;cACEqC,OAAO,EAAEA,CAAA,KAAM3B,mBAAmB,CAAC,IAAI,CAAE;cACzCqB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpC,OAAA,CAACR,IAAI;cACH8C,EAAE,EAAE,oBAAoB7B,gBAAgB,CAACwC,EAAE,EAAG;cAC9ClB,SAAS,EAAC,aAAa;cACvBM,OAAO,EAAEA,CAAA,KAAM3B,mBAAmB,CAAC,IAAI,CAAE;cAAAsB,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClC,EAAA,CA3PID,iBAA2B;AAAA+D,EAAA,GAA3B/D,iBAA2B;AA6PjC,eAAeA,iBAAiB;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}