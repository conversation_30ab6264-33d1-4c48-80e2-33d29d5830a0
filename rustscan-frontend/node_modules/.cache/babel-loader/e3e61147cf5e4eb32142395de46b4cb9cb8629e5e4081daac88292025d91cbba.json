{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileVolume2 = createLucideIcon(\"FileVolume2\", [[\"path\", {\n  d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\",\n  key: \"1nnpy2\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"M11.5 13.5c.32.4.5.94.5 1.5s-.18 1.1-.5 1.5\",\n  key: \"joawwx\"\n}], [\"path\", {\n  d: \"M15 12c.64.8 1 1.87 1 3s-.36 2.2-1 3\",\n  key: \"1f2wyw\"\n}], [\"path\", {\n  d: \"M8 15h.01\",\n  key: \"a7atzg\"\n}]]);\nexport { FileVolume2 as default };", "map": {"version": 3, "names": ["FileVolume2", "createLucideIcon", "d", "key", "points"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/file-volume-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileVolume2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/file-volume-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileVolume2 = createLucideIcon('FileVolume2', [\n  [\n    'path',\n    {\n      d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z',\n      key: '1nnpy2',\n    },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['path', { d: 'M11.5 13.5c.32.4.5.94.5 1.5s-.18 1.1-.5 1.5', key: 'joawwx' }],\n  ['path', { d: 'M15 12c.64.8 1 1.87 1 3s-.36 2.2-1 3', key: '1f2wyw' }],\n  ['path', { d: 'M8 15h.01', key: 'a7atzg' }],\n]);\n\nexport default FileVolume2;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}