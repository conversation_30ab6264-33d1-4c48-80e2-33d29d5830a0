{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Option = createLucideIcon(\"Option\", [[\"path\", {\n  d: \"M3 3h6l6 18h6\",\n  key: \"ph9rgk\"\n}], [\"path\", {\n  d: \"M14 3h7\",\n  key: \"16f0ms\"\n}]]);\nexport { Option as default };", "map": {"version": 3, "names": ["Option", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/option.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Option\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzaDZsNiAxOGg2IiAvPgogIDxwYXRoIGQ9Ik0xNCAzaDciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/option\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Option = createLucideIcon('Option', [\n  ['path', { d: 'M3 3h6l6 18h6', key: 'ph9rgk' }],\n  ['path', { d: 'M14 3h7', key: '16f0ms' }],\n]);\n\nexport default Option;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}