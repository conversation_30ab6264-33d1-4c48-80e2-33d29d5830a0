{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Newspaper = createLucideIcon(\"Newspaper\", [[\"path\", {\n  d: \"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2\",\n  key: \"7pis2x\"\n}], [\"path\", {\n  d: \"M18 14h-8\",\n  key: \"sponae\"\n}], [\"path\", {\n  d: \"M15 18h-5\",\n  key: \"95g1m2\"\n}], [\"path\", {\n  d: \"M10 6h8v4h-8V6Z\",\n  key: \"smlsk5\"\n}]]);\nexport { Newspaper as default };", "map": {"version": 3, "names": ["Newspaper", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/newspaper.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Newspaper\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAyMmgxNmEyIDIgMCAwIDAgMi0yVjRhMiAyIDAgMCAwLTItMkg4YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAxLTIgMlptMCAwYTIgMiAwIDAgMS0yLTJ2LTljMC0xLjEuOS0yIDItMmgyIiAvPgogIDxwYXRoIGQ9Ik0xOCAxNGgtOCIgLz4KICA8cGF0aCBkPSJNMTUgMThoLTUiIC8+CiAgPHBhdGggZD0iTTEwIDZoOHY0aC04VjZaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/newspaper\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Newspaper = createLucideIcon('Newspaper', [\n  [\n    'path',\n    {\n      d: 'M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2',\n      key: '7pis2x',\n    },\n  ],\n  ['path', { d: 'M18 14h-8', key: 'sponae' }],\n  ['path', { d: 'M15 18h-5', key: '95g1m2' }],\n  ['path', { d: 'M10 6h8v4h-8V6Z', key: 'smlsk5' }],\n]);\n\nexport default Newspaper;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}