{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Pocket = createLucideIcon(\"Pocket\", [[\"path\", {\n  d: \"M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z\",\n  key: \"1mz881\"\n}], [\"polyline\", {\n  points: \"8 10 12 14 16 10\",\n  key: \"w4mbv5\"\n}]]);\nexport { Pocket as default };", "map": {"version": 3, "names": ["Pocket", "createLucideIcon", "d", "key", "points"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/pocket.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Pocket\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAzaDE2YTIgMiAwIDAgMSAyIDJ2NmExMCAxMCAwIDAgMS0xMCAxMEExMCAxMCAwIDAgMSAyIDExVjVhMiAyIDAgMCAxIDItMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iOCAxMCAxMiAxNCAxNiAxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pocket\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pocket = createLucideIcon('Pocket', [\n  [\n    'path',\n    {\n      d: 'M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z',\n      key: '1mz881',\n    },\n  ],\n  ['polyline', { points: '8 10 12 14 16 10', key: 'w4mbv5' }],\n]);\n\nexport default Pocket;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}