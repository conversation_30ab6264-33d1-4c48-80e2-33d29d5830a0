{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Dashboard from './components/Dashboard';\nimport WorkflowTemplates from './components/WorkflowTemplates';\nimport WorkflowInstances from './components/WorkflowInstances';\nimport WorkflowDetail from './components/WorkflowDetail';\nimport CreateWorkflow from './components/CreateWorkflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/templates\",\n          element: /*#__PURE__*/_jsxDEV(WorkflowTemplates, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/instances\",\n          element: /*#__PURE__*/_jsxDEV(WorkflowInstances, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/instances/:id\",\n          element: /*#__PURE__*/_jsxDEV(WorkflowDetail, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/create\",\n          element: /*#__PURE__*/_jsxDEV(CreateWorkflow, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Layout", "Dashboard", "WorkflowTemplates", "WorkflowInstances", "WorkflowDetail", "CreateWorkflow", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Dashboard from './components/Dashboard';\nimport WorkflowTemplates from './components/WorkflowTemplates';\nimport WorkflowInstances from './components/WorkflowInstances';\nimport WorkflowDetail from './components/WorkflowDetail';\nimport CreateWorkflow from './components/CreateWorkflow';\nimport { NotificationProvider } from './components/NotificationSystem';\n\nfunction App() {\n  return (\n    <Router>\n      <Layout>\n        <Routes>\n          <Route path=\"/\" element={<Dashboard />} />\n          <Route path=\"/templates\" element={<WorkflowTemplates />} />\n          <Route path=\"/instances\" element={<WorkflowInstances />} />\n          <Route path=\"/instances/:id\" element={<WorkflowDetail />} />\n          <Route path=\"/create\" element={<CreateWorkflow />} />\n        </Routes>\n      </Layout>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,cAAc,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,MAAM;IAAAY,QAAA,eACLF,OAAA,CAACP,MAAM;MAAAS,QAAA,eACLF,OAAA,CAACT,MAAM;QAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACN,SAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEJ,OAAA,CAACL,iBAAiB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEJ,OAAA,CAACJ,iBAAiB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAEJ,OAAA,CAACH,cAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,SAAS;UAACC,OAAO,eAAEJ,OAAA,CAACF,cAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GAdQR,GAAG;AAgBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}