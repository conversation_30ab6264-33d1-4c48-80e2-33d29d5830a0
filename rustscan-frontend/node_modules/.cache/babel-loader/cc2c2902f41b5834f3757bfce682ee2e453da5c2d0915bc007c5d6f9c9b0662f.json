{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Coffee = createLucideIcon(\"Coffee\", [[\"path\", {\n  d: \"M17 8h1a4 4 0 1 1 0 8h-1\",\n  key: \"jx4kbh\"\n}], [\"path\", {\n  d: \"M3 8h14v9a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4Z\",\n  key: \"1bxrl0\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"2\",\n  y2: \"4\",\n  key: \"1cr9l3\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"10\",\n  y1: \"2\",\n  y2: \"4\",\n  key: \"170wym\"\n}], [\"line\", {\n  x1: \"14\",\n  x2: \"14\",\n  y1: \"2\",\n  y2: \"4\",\n  key: \"1c5f70\"\n}]]);\nexport { Coffee as default };", "map": {"version": 3, "names": ["Coffee", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/coffee.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Coffee\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgOGgxYTQgNCAwIDEgMSAwIDhoLTEiIC8+CiAgPHBhdGggZD0iTTMgOGgxNHY5YTQgNCAwIDAgMS00IDRIN2E0IDQgMCAwIDEtNC00WiIgLz4KICA8bGluZSB4MT0iNiIgeDI9IjYiIHkxPSIyIiB5Mj0iNCIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjIiIHkyPSI0IiAvPgogIDxsaW5lIHgxPSIxNCIgeDI9IjE0IiB5MT0iMiIgeTI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/coffee\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Coffee = createLucideIcon('Coffee', [\n  ['path', { d: 'M17 8h1a4 4 0 1 1 0 8h-1', key: 'jx4kbh' }],\n  ['path', { d: 'M3 8h14v9a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4Z', key: '1bxrl0' }],\n  ['line', { x1: '6', x2: '6', y1: '2', y2: '4', key: '1cr9l3' }],\n  ['line', { x1: '10', x2: '10', y1: '2', y2: '4', key: '170wym' }],\n  ['line', { x1: '14', x2: '14', y1: '2', y2: '4', key: '1c5f70' }],\n]);\n\nexport default Coffee;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}