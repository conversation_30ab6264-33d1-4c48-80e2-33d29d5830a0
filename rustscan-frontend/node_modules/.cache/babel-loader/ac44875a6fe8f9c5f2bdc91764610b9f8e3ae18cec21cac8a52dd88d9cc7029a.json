{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst UserCog = createLucideIcon(\"UserCog\", [[\"path\", {\n  d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n  key: \"1yyitq\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"nufk8\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"1rxg02\"\n}], [\"path\", {\n  d: \"M19 8v1\",\n  key: \"1iffrw\"\n}], [\"path\", {\n  d: \"M19 13v1\",\n  key: \"z4xc62\"\n}], [\"path\", {\n  d: \"m21.6 9.5-.87.5\",\n  key: \"6lxupl\"\n}], [\"path\", {\n  d: \"m17.27 12-.87.5\",\n  key: \"1rwhxx\"\n}], [\"path\", {\n  d: \"m21.6 12.5-.87-.5\",\n  key: \"agvc9a\"\n}], [\"path\", {\n  d: \"m17.27 10-.87-.5\",\n  key: \"12d57s\"\n}]]);\nexport { UserCog as default };", "map": {"version": 3, "names": ["UserCog", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/node_modules/lucide-react/src/icons/user-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8Y2lyY2xlIGN4PSIxOSIgY3k9IjExIiByPSIyIiAvPgogIDxwYXRoIGQ9Ik0xOSA4djEiIC8+CiAgPHBhdGggZD0iTTE5IDEzdjEiIC8+CiAgPHBhdGggZD0ibTIxLjYgOS41LS44Ny41IiAvPgogIDxwYXRoIGQ9Im0xNy4yNyAxMi0uODcuNSIgLz4KICA8cGF0aCBkPSJtMjEuNiAxMi41LS44Ny0uNSIgLz4KICA8cGF0aCBkPSJtMTcuMjcgMTAtLjg3LS41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/user-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserCog = createLucideIcon('UserCog', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['circle', { cx: '19', cy: '11', r: '2', key: '1rxg02' }],\n  ['path', { d: 'M19 8v1', key: '1iffrw' }],\n  ['path', { d: 'M19 13v1', key: 'z4xc62' }],\n  ['path', { d: 'm21.6 9.5-.87.5', key: '6lxupl' }],\n  ['path', { d: 'm17.27 12-.87.5', key: '1rwhxx' }],\n  ['path', { d: 'm21.6 12.5-.87-.5', key: 'agvc9a' }],\n  ['path', { d: 'm17.27 10-.87-.5', key: '12d57s' }],\n]);\n\nexport default UserCog;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAS,GACrD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}