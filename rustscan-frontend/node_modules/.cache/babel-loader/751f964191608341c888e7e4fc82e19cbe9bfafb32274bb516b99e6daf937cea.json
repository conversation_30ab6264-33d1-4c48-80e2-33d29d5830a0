{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Home, FileText, Play, Menu, X, Shield, Zap, Settings, Bell, Search, User, ChevronDown } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [userMenuOpen, setUserMenuOpen] = useState(false);\n  const location = useLocation();\n  const navigation = [{\n    name: '概览',\n    href: '/',\n    icon: Home,\n    description: '系统状态和统计信息'\n  }, {\n    name: '工作流模板',\n    href: '/templates',\n    icon: FileText,\n    description: '预定义的扫描模板'\n  }, {\n    name: '扫描实例',\n    href: '/instances',\n    icon: Play,\n    description: '正在运行和历史扫描'\n  }, {\n    name: '创建扫描',\n    href: '/create',\n    icon: Zap,\n    description: '启动新的安全扫描'\n  }];\n  const isActive = href => {\n    if (href === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(href);\n  };\n  const getPageTitle = () => {\n    const currentNav = navigation.find(nav => isActive(nav.href));\n    return (currentNav === null || currentNav === void 0 ? void 0 : currentNav.name) || 'RustScan';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background\",\n    children: [sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-40 bg-black/50 lg:hidden\",\n      onClick: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n      className: `\n        fixed inset-y-0 left-0 z-50 w-72 bg-card border-r border-border transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16 px-6 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              className: \"h-8 w-8 text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full animate-pulse-glow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg font-bold text-foreground\",\n              children: \"RustScan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-muted-foreground\",\n              children: \"\\u5B89\\u5168\\u626B\\u63CF\\u5E73\\u53F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSidebarOpen(false),\n          className: \"lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 px-4 py-6 space-y-2\",\n        children: navigation.map(item => {\n          const Icon = item.icon;\n          const active = isActive(item.href);\n          return /*#__PURE__*/_jsxDEV(Link, {\n            to: item.href,\n            className: `\n                  group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-accent\n                  ${active ? 'bg-primary text-primary-foreground shadow-sm' : 'text-muted-foreground hover:text-foreground'}\n                `,\n            onClick: () => setSidebarOpen(false),\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: `\n                  mr-3 h-5 w-5 flex-shrink-0\n                  ${active ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'}\n                `\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: active ? 'text-primary-foreground' : 'text-foreground',\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs ${active ? 'text-primary-foreground/80' : 'text-muted-foreground'}`,\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this)]\n          }, item.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-border\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-accent rounded-lg p-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-2 w-2 bg-green-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-foreground\",\n                children: \"\\u7CFB\\u7EDF\\u72B6\\u6001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge-success\",\n              children: \"\\u5728\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-xs text-muted-foreground\",\n            children: \"\\u6240\\u6709\\u670D\\u52A1\\u6B63\\u5E38\\u8FD0\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:pl-72\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16 px-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSidebarOpen(true),\n              className: \"lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\",\n              children: /*#__PURE__*/_jsxDEV(Menu, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-semibold text-foreground\",\n                children: getPageTitle()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-muted-foreground hidden sm:block\",\n                children: \"\\u73B0\\u4EE3\\u5316\\u7684\\u7F51\\u7EDC\\u5B89\\u5168\\u626B\\u63CF\\u5E73\\u53F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\",\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"relative p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\",\n              children: [/*#__PURE__*/_jsxDEV(Bell, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setUserMenuOpen(!userMenuOpen),\n                className: \"flex items-center space-x-2 p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-8 w-8 bg-primary rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(User, {\n                    className: \"h-4 w-4 text-primary-foreground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), userMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg py-1 z-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-3 py-2 border-b border-border\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-foreground\",\n                    children: \"\\u7BA1\\u7406\\u5458\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-muted-foreground\",\n                    children: \"<EMAIL>\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex items-center w-full px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-accent\",\n                  children: [/*#__PURE__*/_jsxDEV(Settings, {\n                    className: \"h-4 w-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 23\n                  }, this), \"\\u8BBE\\u7F6E\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 p-6 animate-fade-in\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"Bg6pX75dsY3n9Jcu8XS8Zoirrbc=\", false, function () {\n  return [useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "Home", "FileText", "Play", "<PERSON><PERSON>", "X", "Shield", "Zap", "Settings", "Bell", "Search", "User", "ChevronDown", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "sidebarOpen", "setSidebarOpen", "userMenuOpen", "setUserMenuOpen", "location", "navigation", "name", "href", "icon", "description", "isActive", "pathname", "startsWith", "getPageTitle", "currentNav", "find", "nav", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "Icon", "active", "to", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/components/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  Home,\n  FileText,\n  Play,\n  Plus,\n  Menu,\n  X,\n  Shield,\n  Activity,\n  Zap,\n  Settings,\n  Bell,\n  Search,\n  User,\n  ChevronDown\n} from 'lucide-react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [userMenuOpen, setUserMenuOpen] = useState(false);\n  const location = useLocation();\n\n  const navigation = [\n    {\n      name: '概览',\n      href: '/',\n      icon: Home,\n      description: '系统状态和统计信息'\n    },\n    {\n      name: '工作流模板',\n      href: '/templates',\n      icon: FileText,\n      description: '预定义的扫描模板'\n    },\n    {\n      name: '扫描实例',\n      href: '/instances',\n      icon: Play,\n      description: '正在运行和历史扫描'\n    },\n    {\n      name: '创建扫描',\n      href: '/create',\n      icon: Zap,\n      description: '启动新的安全扫描'\n    },\n  ];\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return location.pathname === '/';\n    }\n    return location.pathname.startsWith(href);\n  };\n\n  const getPageTitle = () => {\n    const currentNav = navigation.find(nav => isActive(nav.href));\n    return currentNav?.name || 'RustScan';\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* 移动端侧边栏背景 */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black/50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* 侧边栏 */}\n      <aside className={`\n        fixed inset-y-0 left-0 z-50 w-72 bg-card border-r border-border transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        {/* 品牌区域 */}\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-border\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"relative\">\n              <Shield className=\"h-8 w-8 text-primary\" />\n              <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full animate-pulse-glow\"></div>\n            </div>\n            <div>\n              <h1 className=\"text-lg font-bold text-foreground\">RustScan</h1>\n              <p className=\"text-xs text-muted-foreground\">安全扫描平台</p>\n            </div>\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2\">\n          {navigation.map((item) => {\n            const Icon = item.icon;\n            const active = isActive(item.href);\n            return (\n              <Link\n                key={item.name}\n                to={item.href}\n                className={`\n                  group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-accent\n                  ${active\n                    ? 'bg-primary text-primary-foreground shadow-sm'\n                    : 'text-muted-foreground hover:text-foreground'\n                  }\n                `}\n                onClick={() => setSidebarOpen(false)}\n              >\n                <Icon className={`\n                  mr-3 h-5 w-5 flex-shrink-0\n                  ${active ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'}\n                `} />\n                <div className=\"flex-1\">\n                  <div className={active ? 'text-primary-foreground' : 'text-foreground'}>{item.name}</div>\n                  <div className={`text-xs ${active ? 'text-primary-foreground/80' : 'text-muted-foreground'}`}>\n                    {item.description}\n                  </div>\n                </div>\n              </Link>\n            );\n          })}\n        </nav>\n\n        {/* 状态指示器 */}\n        <div className=\"p-4 border-t border-border\">\n          <div className=\"bg-accent rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"h-2 w-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm font-medium text-foreground\">系统状态</span>\n              </div>\n              <span className=\"badge-success\">在线</span>\n            </div>\n            <div className=\"mt-2 text-xs text-muted-foreground\">\n              所有服务正常运行\n            </div>\n          </div>\n        </div>\n      </aside>\n\n      {/* 主内容区域 */}\n      <div className=\"lg:pl-72\">\n        {/* 顶部导航栏 */}\n        <header className=\"sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border\">\n          <div className=\"flex items-center justify-between h-16 px-6\">\n            {/* 移动端菜单按钮 */}\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\"\n              >\n                <Menu className=\"h-5 w-5\" />\n              </button>\n\n              {/* 页面标题 */}\n              <div>\n                <h1 className=\"text-xl font-semibold text-foreground\">\n                  {getPageTitle()}\n                </h1>\n                <p className=\"text-sm text-muted-foreground hidden sm:block\">\n                  现代化的网络安全扫描平台\n                </p>\n              </div>\n            </div>\n\n            {/* 右侧操作区 */}\n            <div className=\"flex items-center space-x-4\">\n              {/* 搜索按钮 */}\n              <button className=\"p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\">\n                <Search className=\"h-5 w-5\" />\n              </button>\n\n              {/* 通知按钮 */}\n              <button className=\"relative p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\">\n                <Bell className=\"h-5 w-5\" />\n                <div className=\"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full\"></div>\n              </button>\n\n              {/* 用户菜单 */}\n              <div className=\"relative\">\n                <button\n                  onClick={() => setUserMenuOpen(!userMenuOpen)}\n                  className=\"flex items-center space-x-2 p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent\"\n                >\n                  <div className=\"h-8 w-8 bg-primary rounded-full flex items-center justify-center\">\n                    <User className=\"h-4 w-4 text-primary-foreground\" />\n                  </div>\n                  <ChevronDown className=\"h-4 w-4\" />\n                </button>\n\n                {/* 用户下拉菜单 */}\n                {userMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg py-1 z-50\">\n                    <div className=\"px-3 py-2 border-b border-border\">\n                      <p className=\"text-sm font-medium text-foreground\">管理员</p>\n                      <p className=\"text-xs text-muted-foreground\"><EMAIL></p>\n                    </div>\n                    <button className=\"flex items-center w-full px-3 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-accent\">\n                      <Settings className=\"h-4 w-4 mr-2\" />\n                      设置\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* 页面内容 */}\n        <main className=\"flex-1 p-6 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,IAAI,EACJC,QAAQ,EACRC,IAAI,EAEJC,IAAI,EACJC,CAAC,EACDC,MAAM,EAENC,GAAG,EACHC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,WAAW,QACN,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtB,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMwB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAMuB,UAAU,GAAG,CACjB;IACEC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAEzB,IAAI;IACV0B,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAExB,QAAQ;IACdyB,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAEvB,IAAI;IACVwB,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAEnB,GAAG;IACToB,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,QAAQ,GAAIH,IAAY,IAAK;IACjC,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,OAAOH,QAAQ,CAACO,QAAQ,KAAK,GAAG;IAClC;IACA,OAAOP,QAAQ,CAACO,QAAQ,CAACC,UAAU,CAACL,IAAI,CAAC;EAC3C,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAGT,UAAU,CAACU,IAAI,CAACC,GAAG,IAAIN,QAAQ,CAACM,GAAG,CAACT,IAAI,CAAC,CAAC;IAC7D,OAAO,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAER,IAAI,KAAI,UAAU;EACvC,CAAC;EAED,oBACEV,OAAA;IAAKqB,SAAS,EAAC,4BAA4B;IAAAnB,QAAA,GAExCE,WAAW,iBACVJ,OAAA;MACEqB,SAAS,EAAC,0CAA0C;MACpDC,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,KAAK;IAAE;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,eAGD1B,OAAA;MAAOqB,SAAS,EAAE;AACxB;AACA,UAAUjB,WAAW,GAAG,eAAe,GAAG,mBAAmB;AAC7D,OAAQ;MAAAF,QAAA,gBAEAF,OAAA;QAAKqB,SAAS,EAAC,oEAAoE;QAAAnB,QAAA,gBACjFF,OAAA;UAAKqB,SAAS,EAAC,6BAA6B;UAAAnB,QAAA,gBAC1CF,OAAA;YAAKqB,SAAS,EAAC,UAAU;YAAAnB,QAAA,gBACvBF,OAAA,CAACR,MAAM;cAAC6B,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C1B,OAAA;cAAKqB,SAAS,EAAC;YAA+E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACN1B,OAAA;YAAAE,QAAA,gBACEF,OAAA;cAAIqB,SAAS,EAAC,mCAAmC;cAAAnB,QAAA,EAAC;YAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D1B,OAAA;cAAGqB,SAAS,EAAC,+BAA+B;cAAAnB,QAAA,EAAC;YAAM;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1B,OAAA;UACEsB,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,KAAK,CAAE;UACrCgB,SAAS,EAAC,sFAAsF;UAAAnB,QAAA,eAEhGF,OAAA,CAACT,CAAC;YAAC8B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1B,OAAA;QAAKqB,SAAS,EAAC,4BAA4B;QAAAnB,QAAA,EACxCO,UAAU,CAACkB,GAAG,CAAEC,IAAI,IAAK;UACxB,MAAMC,IAAI,GAAGD,IAAI,CAAChB,IAAI;UACtB,MAAMkB,MAAM,GAAGhB,QAAQ,CAACc,IAAI,CAACjB,IAAI,CAAC;UAClC,oBACEX,OAAA,CAACf,IAAI;YAEH8C,EAAE,EAAEH,IAAI,CAACjB,IAAK;YACdU,SAAS,EAAE;AAC3B;AACA,oBAAoBS,MAAM,GACJ,8CAA8C,GAC9C,6CAA6C;AACnE,iBACkB;YACFR,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,KAAK,CAAE;YAAAH,QAAA,gBAErCF,OAAA,CAAC6B,IAAI;cAACR,SAAS,EAAE;AACjC;AACA,oBAAoBS,MAAM,GAAG,yBAAyB,GAAG,mDAAmD;AAC5G;YAAkB;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACL1B,OAAA;cAAKqB,SAAS,EAAC,QAAQ;cAAAnB,QAAA,gBACrBF,OAAA;gBAAKqB,SAAS,EAAES,MAAM,GAAG,yBAAyB,GAAG,iBAAkB;gBAAA5B,QAAA,EAAE0B,IAAI,CAAClB;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzF1B,OAAA;gBAAKqB,SAAS,EAAE,WAAWS,MAAM,GAAG,4BAA4B,GAAG,uBAAuB,EAAG;gBAAA5B,QAAA,EAC1F0B,IAAI,CAACf;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GApBDE,IAAI,CAAClB,IAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBV,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1B,OAAA;QAAKqB,SAAS,EAAC,4BAA4B;QAAAnB,QAAA,eACzCF,OAAA;UAAKqB,SAAS,EAAC,0BAA0B;UAAAnB,QAAA,gBACvCF,OAAA;YAAKqB,SAAS,EAAC,mCAAmC;YAAAnB,QAAA,gBAChDF,OAAA;cAAKqB,SAAS,EAAC,6BAA6B;cAAAnB,QAAA,gBAC1CF,OAAA;gBAAKqB,SAAS,EAAC;cAAiD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvE1B,OAAA;gBAAMqB,SAAS,EAAC,qCAAqC;gBAAAnB,QAAA,EAAC;cAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACN1B,OAAA;cAAMqB,SAAS,EAAC,eAAe;cAAAnB,QAAA,EAAC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN1B,OAAA;YAAKqB,SAAS,EAAC,oCAAoC;YAAAnB,QAAA,EAAC;UAEpD;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR1B,OAAA;MAAKqB,SAAS,EAAC,UAAU;MAAAnB,QAAA,gBAEvBF,OAAA;QAAQqB,SAAS,EAAC,qHAAqH;QAAAnB,QAAA,eACrIF,OAAA;UAAKqB,SAAS,EAAC,6CAA6C;UAAAnB,QAAA,gBAE1DF,OAAA;YAAKqB,SAAS,EAAC,6BAA6B;YAAAnB,QAAA,gBAC1CF,OAAA;cACEsB,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,IAAI,CAAE;cACpCgB,SAAS,EAAC,sFAAsF;cAAAnB,QAAA,eAEhGF,OAAA,CAACV,IAAI;gBAAC+B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAGT1B,OAAA;cAAAE,QAAA,gBACEF,OAAA;gBAAIqB,SAAS,EAAC,uCAAuC;gBAAAnB,QAAA,EAClDe,YAAY,CAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACL1B,OAAA;gBAAGqB,SAAS,EAAC,+CAA+C;gBAAAnB,QAAA,EAAC;cAE7D;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1B,OAAA;YAAKqB,SAAS,EAAC,6BAA6B;YAAAnB,QAAA,gBAE1CF,OAAA;cAAQqB,SAAS,EAAC,4EAA4E;cAAAnB,QAAA,eAC5FF,OAAA,CAACJ,MAAM;gBAACyB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eAGT1B,OAAA;cAAQqB,SAAS,EAAC,qFAAqF;cAAAnB,QAAA,gBACrGF,OAAA,CAACL,IAAI;gBAAC0B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5B1B,OAAA;gBAAKqB,SAAS,EAAC;cAAwD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAGT1B,OAAA;cAAKqB,SAAS,EAAC,UAAU;cAAAnB,QAAA,gBACvBF,OAAA;gBACEsB,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9Ce,SAAS,EAAC,wGAAwG;gBAAAnB,QAAA,gBAElHF,OAAA;kBAAKqB,SAAS,EAAC,kEAAkE;kBAAAnB,QAAA,eAC/EF,OAAA,CAACH,IAAI;oBAACwB,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN1B,OAAA,CAACF,WAAW;kBAACuB,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EAGRpB,YAAY,iBACXN,OAAA;gBAAKqB,SAAS,EAAC,2FAA2F;gBAAAnB,QAAA,gBACxGF,OAAA;kBAAKqB,SAAS,EAAC,kCAAkC;kBAAAnB,QAAA,gBAC/CF,OAAA;oBAAGqB,SAAS,EAAC,qCAAqC;oBAAAnB,QAAA,EAAC;kBAAG;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1D1B,OAAA;oBAAGqB,SAAS,EAAC,+BAA+B;oBAAAnB,QAAA,EAAC;kBAAkB;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACN1B,OAAA;kBAAQqB,SAAS,EAAC,wGAAwG;kBAAAnB,QAAA,gBACxHF,OAAA,CAACN,QAAQ;oBAAC2B,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGT1B,OAAA;QAAMqB,SAAS,EAAC,4BAA4B;QAAAnB,QAAA,eAC1CF,OAAA;UAAKqB,SAAS,EAAC,mBAAmB;UAAAnB,QAAA,EAC/BA;QAAQ;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA9MIF,MAA6B;EAAA,QAGhBf,WAAW;AAAA;AAAA8C,EAAA,GAHxB/B,MAA6B;AAgNnC,eAAeA,MAAM;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}