{"ast": null, "code": "import axios from 'axios';\n\n// API 基础配置\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  console.log(`API Response: ${response.status} ${response.config.url}`);\n  return response;\n}, error => {\n  var _error$response;\n  console.error('API Error:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n  return Promise.reject(error);\n});\n\n// 类型定义\n\n// API 方法\nexport const workflowApi = {\n  // 获取健康状态\n  getHealth: async () => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n  // 获取工作流模板\n  getWorkflowTemplates: async () => {\n    const response = await api.get('/workflow-templates');\n    return response.data;\n  },\n  // 获取工作流实例列表\n  getWorkflowInstances: async () => {\n    const response = await api.get('/workflow-instances');\n    return response.data;\n  },\n  // 创建工作流实例\n  createWorkflowInstance: async request => {\n    const response = await api.post('/workflow-instances', request);\n    return response.data;\n  },\n  // 获取工作流状态\n  getWorkflowStatus: async instanceId => {\n    const response = await api.get(`/workflow-instances/${instanceId}/status`);\n    return response.data;\n  },\n  // 获取工作流统计\n  getWorkflowStatistics: async () => {\n    const response = await api.get('/workflow-statistics');\n    return response.data;\n  },\n  // 停止工作流实例\n  stopInstance: async instanceId => {\n    const response = await api.post(`/workflow-instances/${instanceId}/stop`);\n    return response.data;\n  },\n  // 重新运行工作流实例\n  restartInstance: async instanceId => {\n    const response = await api.post(`/workflow-instances/${instanceId}/restart`);\n    return response.data;\n  },\n  // 删除工作流实例\n  deleteInstance: async instanceId => {\n    const response = await api.delete(`/workflow-instances/${instanceId}`);\n    return response.data;\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "_error$response", "data", "message", "workflowApi", "getHealth", "get", "getWorkflowTemplates", "getWorkflowInstances", "createWorkflowInstance", "post", "getWorkflowStatus", "instanceId", "getWorkflowStatistics", "stopInstance", "restartInstance", "deleteInstance", "delete"], "sources": ["/Users/<USER>/Downloads/scan/RustScan-master/rustscan-frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// API 基础配置\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api/v1';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    console.log(`API Response: ${response.status} ${response.config.url}`);\n    return response;\n  },\n  (error) => {\n    console.error('API Error:', error.response?.data || error.message);\n    return Promise.reject(error);\n  }\n);\n\n// 类型定义\nexport interface WorkflowStep {\n  id: string;\n  name: string;\n  tool: string;\n  parameters: Record<string, any>;\n  depends_on: string[];\n}\n\nexport interface WorkflowDefinition {\n  id: string;\n  name: string;\n  description: string;\n  version: string;\n  steps: WorkflowStep[];\n  global_config?: Record<string, any>;\n}\n\nexport interface WorkflowProgress {\n  completed_steps: number;\n  percentage: number;\n  total_steps: number;\n}\n\nexport interface WorkflowInstance {\n  instance_id: string;\n  workflow_id: string;\n  name: string;\n  target: string;\n  status: string;\n  current_step?: string;\n  progress?: number | WorkflowProgress;\n  created_at: string;\n  started_at?: string;\n  completed_at?: string;\n}\n\nexport interface WorkflowStepInstance {\n  id: string;\n  name: string;\n  status: string;\n  tool: string;\n  started_at?: string;\n  completed_at?: string;\n  execution_time?: number;\n}\n\nexport interface WorkflowStatus {\n  instance_id: string;\n  status: string;\n  current_step?: string;\n  progress: {\n    completed_steps: number;\n    total_steps: number;\n    percentage: number;\n  };\n  steps: WorkflowStepInstance[];\n  created_at: string;\n  started_at?: string;\n}\n\nexport interface CreateWorkflowInstanceRequest {\n  workflow_id: string;\n  name: string;\n  target: string;\n  context?: Record<string, any>;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: {\n    code: string;\n    message: string;\n  };\n}\n\n// API 方法\nexport const workflowApi = {\n  // 获取健康状态\n  getHealth: async () => {\n    const response = await api.get('/health');\n    return response.data;\n  },\n\n  // 获取工作流模板\n  getWorkflowTemplates: async (): Promise<ApiResponse<{ templates: WorkflowDefinition[]; count: number }>> => {\n    const response = await api.get('/workflow-templates');\n    return response.data;\n  },\n\n  // 获取工作流实例列表\n  getWorkflowInstances: async (): Promise<ApiResponse<{ instances: WorkflowInstance[]; count: number }>> => {\n    const response = await api.get('/workflow-instances');\n    return response.data;\n  },\n\n  // 创建工作流实例\n  createWorkflowInstance: async (request: CreateWorkflowInstanceRequest): Promise<ApiResponse<WorkflowInstance>> => {\n    const response = await api.post('/workflow-instances', request);\n    return response.data;\n  },\n\n  // 获取工作流状态\n  getWorkflowStatus: async (instanceId: string): Promise<ApiResponse<WorkflowStatus>> => {\n    const response = await api.get(`/workflow-instances/${instanceId}/status`);\n    return response.data;\n  },\n\n  // 获取工作流统计\n  getWorkflowStatistics: async () => {\n    const response = await api.get('/workflow-statistics');\n    return response.data;\n  },\n\n  // 停止工作流实例\n  stopInstance: async (instanceId: string): Promise<ApiResponse<{ instance_id: string; message: string; status: string }>> => {\n    const response = await api.post(`/workflow-instances/${instanceId}/stop`);\n    return response.data;\n  },\n\n  // 重新运行工作流实例\n  restartInstance: async (instanceId: string): Promise<ApiResponse<{ instance_id: string; original_instance_id: string; message: string; status: string }>> => {\n    const response = await api.post(`/workflow-instances/${instanceId}/restart`);\n    return response.data;\n  },\n\n  // 删除工作流实例\n  deleteInstance: async (instanceId: string): Promise<ApiResponse<{ instance_id: string; message: string }>> => {\n    const response = await api.delete(`/workflow-instances/${instanceId}`);\n    return response.data;\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;AAEpF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,iBAAAF,cAAA,GAAgBD,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIL,MAAM,CAACM,GAAG,EAAE,CAAC;EACzE,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACK,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZR,OAAO,CAACC,GAAG,CAAC,iBAAiBO,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACV,MAAM,CAACM,GAAG,EAAE,CAAC;EACtE,OAAOI,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA;EACTV,OAAO,CAACK,KAAK,CAAC,YAAY,EAAE,EAAAK,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,IAAI,KAAIN,KAAK,CAACO,OAAO,CAAC;EAClE,OAAON,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AA6EA;AACA,OAAO,MAAMQ,WAAW,GAAG;EACzB;EACAC,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAMN,QAAQ,GAAG,MAAMlB,GAAG,CAACyB,GAAG,CAAC,SAAS,CAAC;IACzC,OAAOP,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAK,oBAAoB,EAAE,MAAAA,CAAA,KAAsF;IAC1G,MAAMR,QAAQ,GAAG,MAAMlB,GAAG,CAACyB,GAAG,CAAC,qBAAqB,CAAC;IACrD,OAAOP,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAM,oBAAoB,EAAE,MAAAA,CAAA,KAAoF;IACxG,MAAMT,QAAQ,GAAG,MAAMlB,GAAG,CAACyB,GAAG,CAAC,qBAAqB,CAAC;IACrD,OAAOP,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAO,sBAAsB,EAAE,MAAOtB,OAAsC,IAA6C;IAChH,MAAMY,QAAQ,GAAG,MAAMlB,GAAG,CAAC6B,IAAI,CAAC,qBAAqB,EAAEvB,OAAO,CAAC;IAC/D,OAAOY,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAS,iBAAiB,EAAE,MAAOC,UAAkB,IAA2C;IACrF,MAAMb,QAAQ,GAAG,MAAMlB,GAAG,CAACyB,GAAG,CAAC,uBAAuBM,UAAU,SAAS,CAAC;IAC1E,OAAOb,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAW,qBAAqB,EAAE,MAAAA,CAAA,KAAY;IACjC,MAAMd,QAAQ,GAAG,MAAMlB,GAAG,CAACyB,GAAG,CAAC,sBAAsB,CAAC;IACtD,OAAOP,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAY,YAAY,EAAE,MAAOF,UAAkB,IAAqF;IAC1H,MAAMb,QAAQ,GAAG,MAAMlB,GAAG,CAAC6B,IAAI,CAAC,uBAAuBE,UAAU,OAAO,CAAC;IACzE,OAAOb,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAa,eAAe,EAAE,MAAOH,UAAkB,IAAmH;IAC3J,MAAMb,QAAQ,GAAG,MAAMlB,GAAG,CAAC6B,IAAI,CAAC,uBAAuBE,UAAU,UAAU,CAAC;IAC5E,OAAOb,QAAQ,CAACG,IAAI;EACtB,CAAC;EAED;EACAc,cAAc,EAAE,MAAOJ,UAAkB,IAAqE;IAC5G,MAAMb,QAAQ,GAAG,MAAMlB,GAAG,CAACoC,MAAM,CAAC,uBAAuBL,UAAU,EAAE,CAAC;IACtE,OAAOb,QAAQ,CAACG,IAAI;EACtB;AACF,CAAC;AAED,eAAerB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}